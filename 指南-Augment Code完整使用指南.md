---
title: 指南-Augment Code完整使用指南
created: 2025-06-22
modified: 2025-06-22
version: "1.0"
tags: [主題/程式設計, 類型/指南, 狀態/已完成, 備註/AI生成]
aliases: [Augment使用手冊, AI編程助手指南, Augment完整教程]
---

> [!note] 彙整者備註:
> 

## 🎯 TL;DR 快速摘要

Augment Code是一款強大的AI編程助手，整合於VS Code和JetBrains IDE中，提供**Agent自動化開發**、**Chat智能對話**、**Next Edit變更建議**、**Completions代碼補全**和**Instructions自然語言指令**五大核心功能。透過深度workspace索引和外部整合，實現從想法到部署的全流程AI輔助開發。

---

## 章節樹快速導航

- **[[#🚀 快速開始]]**
	├─── [[#安裝與基本設置]]
	├─── [[#鍵盤快捷鍵配置]]
	└─── [[#Workspace索引設置]]

- **[[#🤖 核心功能詳解]]**
	├─── [[#Agent - 自動化開發助手]]
	├─── [[#Chat - 智能代碼對話]]
	├─── [[#Next Edit - 智能變更建議]]
	├─── [[#Instructions - 自然語言指令]]
	└─── [[#Completions - AI代碼補全]]

- **[[#⚙️ 進階配置]]**
	├─── [[#MCP伺服器設置]]
	├─── [[#外部服務整合]]
	├─── [[#Guidelines自訂規則]]
	└─── [[#Remote Agent雲端代理]]

- **[[#🔧 實用技巧與最佳實踐]]**
	├─── [[#提示詞優化策略]]
	├─── [[#Context管理技巧]]
	└─── [[#常見問題解決]]

- **[[#🌟 CloudBase整合方案]]**

---

## 🚀 快速開始

### 安裝與基本設置

> [!tip] 安裝要求
> - 支援平台：Visual Studio Code、JetBrains系列IDE
> - 系統要求：macOS、Windows、Linux
> - 網路要求：需要穩定網路連接進行雲端索引

**安裝步驟：**
1. 從[Augment Code官網](https://augmentcode.com)下載對應平台的擴展
2. 在IDE中啟用Augment擴展
3. 完成帳戶註冊與授權
4. 開啟workspace自動開始索引

### 鍵盤快捷鍵配置

> [!info] macOS快捷鍵

| 功能 | 快捷鍵 | 說明 |
|------|--------|------|
| **開啟Augment面板** | `Cmd + L` | 主要操作入口 |
| **顯示Augment指令** | `Cmd + Shift + A` | 指令選單 |
| **啟動Instructions** | `Cmd + I` | 自然語言指令 |
| **下一個編輯** | `Cmd + ;` | Next Edit功能 |
| **上一個編輯** | `Cmd + Shift + ;` | 返回編輯 |
| **接受補全** | `Tab` | 代碼補全接受 |
| **部分接受補全** | `Cmd + →` | 接受下一個詞 |

> [!info] Windows/Linux快捷鍵

| 功能 | 快捷鍵 | 說明 |
|------|--------|------|
| **開啟Augment面板** | `Ctrl + L` | 主要操作入口 |
| **顯示Augment指令** | `Ctrl + Shift + A` | 指令選單 |
| **啟動Instructions** | `Ctrl + I` | 自然語言指令 |

### Workspace索引設置

**自動索引機制：**
- 開啟workspace時自動上傳代碼庫至Augment雲端
- 索引時間通常少於1分鐘（視代碼庫大小而定）
- 透過`.gitignore`和`.augmentignore`控制索引範圍

**`.augmentignore`文件配置：**
```gitignore
# 排除測試數據
data/test.json

# 包含被gitignore的依賴（使用!前綴）
!node_modules

# 排除敏感配置
.env
secrets/
```

---

## 🤖 核心功能詳解

### Agent - 自動化開發助手

> [!abstract] Agent概述
> Agent是Augment的核心功能，能夠端到端完成軟體開發任務，從簡單編輯到完整功能實現，自動分解請求並逐步執行。

**Agent vs Agent Auto模式：**
- **Agent標準模式**：執行終端指令或外部整合前會暫停等待確認
- **Agent Auto模式**：更加獨立運作，自動執行文件編輯、終端指令和工具調用

**主要使用場景：**
- 🔧 **快速編輯**：調整配置值、功能開關切換
- 🔄 **重構作業**：在文件間移動函數，保持編碼規範
- ✨ **新功能開發**：從GitHub Issue或Linear Ticket實現完整功能
- 🧪 **測試覆蓋**：為新開發功能生成單元測試
- 📚 **文檔生成**：產生comprehensive文檔和README
- 🔗 **Git整合**：建立分支、提交變更、開啟Pull Request

**操作流程：**
1. 在Augment面板選擇Agent模式
2. 使用自然語言描述需求
3. 可選擇增強提示詞（Enhance Prompt ✨）
4. 查看Agent執行計劃和變更
5. 利用Checkpoint機制管控進度
6. 審查每個action的詳細變更

> [!warning] 注意事項
> - Agent會直接修改代碼庫，建議在版本控制下操作
> - 可隨時點擊Stop中斷Agent執行
> - 使用Checkpoint功能可輕鬆回滾到之前狀態

### Chat - 智能代碼對話

> [!abstract] Chat概述
> Chat功能讓您能夠使用自然語言與代碼庫互動，快速理解陌生代碼、探索架構、解決技術問題。

**Context管理技巧：**
- **@檔案提及**：`@routes.tsx`包含特定文件
- **@資料夾提及**：`@components/`包含整個資料夾
- **@第三方文檔**：`@Next.js`引用外部文檔（支援300+文檔集）
- **程式碼區塊選擇**：反白代碼後自動加入context

**Chat Actions快速指令：**
- `/find`：使用自然語言尋找代碼或功能
- `/explain`：解釋反白的代碼區塊
- `/fix`：針對反白代碼提供改進建議
- `/test`：為反白代碼建議測試方案

**對話管理：**
- 點擊🆕圖示開始新對話
- 點擊📋圖示查看歷史對話
- 每個對話保持獨立context
- 支援代碼區塊智能應用（複製、建立新文件、智能套用）

### Next Edit - 智能變更建議

> [!abstract] Next Edit概述
> 基於您的近期工作和代碼context，自動建議後續可能的變更，讓您能夠快速完成重複性修改工作。

**視覺指示器：**
- **🎯 編輯器標題圖示**：變更顏色表示有可用建議
- **📍 Gutter圖示**：標示將被修改的行數
- **💬 灰色文字提示**：在右側顯示變更摘要
- **💡 提示框**：螢幕外建議的簡要說明

**操作方式：**
- `Cmd/Ctrl + ;`：跳轉到下一個建議
- `Enter`：接受當前建議
- `Backspace`：拒絕當前建議
- `Cmd/Ctrl + Shift + ;`：返回上一個建議

**設定選項：**
- **啟用背景建議**：是否自動生成建議
- **全域背景建議**：透過提示框建議其他文件的變更
- **自動套用**：跳轉時自動應用變更
- **懸停顯示差異**：在hover時顯示變更預覽
- **編輯器高亮建議**：視覺化標示所有建議位置

### Instructions - 自然語言指令

> [!abstract] Instructions概述
> 使用自然語言提示詞插入新代碼或修改現有代碼，透過diff預覽確保變更正確性。

**使用流程：**
1. 選擇要修改的代碼或定位新代碼插入點
2. 按下`Cmd/Ctrl + I`啟動Instructions
3. 在diff視圖中輸入自然語言指令
4. 預覽建議的變更
5. 按`Return`接受或`Esc`拒絕

**指令範例：**
- "Add a getUser function that takes userId as a parameter"
- "Refactor this function to use async/await"
- "Add error handling for this API call"
- "Convert this class component to a functional component"

### Completions - AI代碼補全

> [!abstract] Completions概述
> 整合於IDE原生補全系統，提供context-aware的代碼建議，減少查找文檔的時間。

**接受補全選項：**
- `Tab`：接受完整建議
- `Cmd/Ctrl + →`：接受下一個詞
- **自訂快捷鍵**：接受下一行（建議設置為`Cmd/Ctrl + Shift + →`）
- `Esc`：拒絕建議
- **繼續輸入**：忽略建議

**控制選項：**
- `Cmd/Ctrl + Option + A`（VS Code）：切換自動補全
- `Cmd/Ctrl + Option + 9`（JetBrains）：切換自動補全
- 透過Augment面板選單暫時關閉/開啟

---

## ⚙️ 進階配置

### MCP伺服器設置

> [!abstract] MCP概述
> Model Context Protocol（MCP）是由Anthropic開發的開放標準，被譽為「AI應用程式的USB-C」。MCP採用客戶端-伺服器架構，讓AI模型能夠透過標準化協議安全地存取外部數據源和工具，解決了M×N整合問題。

#### **MCP核心架構與工作原理**

**四大核心組件：**
- **Host（主機）**：用戶交互的AI應用程式（如Augment、Claude Desktop）
- **Client（客戶端）**：存在於Host內部，與特定MCP伺服器維持1:1連接
- **Server（伺服器）**：輕量級程式，透過標準化API向AI模型公開特定能力
- **Base Protocol（基礎協議）**：定義所有組件間的JSON-RPC通信方式

**三大基礎原語：**
- **Tools（工具）**：模型控制的函數調用，用於執行特定動作
- **Resources（資源）**：應用程式控制的數據源，提供上下文信息
- **Prompts（提示詞）**：使用者控制的預定義模板，優化工具使用方式

#### **設定方法與實際配置**

**方法一：Augment設定面板**
1. 點擊Augment面板右上角⚙️圖示
2. 在MCP伺服器區段填入名稱和指令
3. 點擊➕新增更多伺服器

**方法二：直接編輯settings.json**
```json
{
  "augment.advanced": {
    "mcpServers": [
      {
        "name": "filesystem",
        "command": "npx",
        "args": [
          "-y",
          "@modelcontextprotocol/server-filesystem",
          "/Users/<USER>/Desktop",
          "/Users/<USER>/Documents"
        ]
      },
      {
        "name": "github",
        "command": "npx",
        "args": ["@modelcontextprotocol/server-github"]
      },
      {
        "name": "cloudbase",
        "command": "npx",
        "args": ["@cloudbase/cloudbase-mcp@latest"]
      }
    ]
  }
}
```

#### **推薦的安全MCP伺服器**

> [!tip] 本地檔案存取（推薦配置）

**官方Filesystem MCP（npx版本）：**
```json
{
  "name": "filesystem",
  "command": "npx",
  "args": [
    "-y",
    "@modelcontextprotocol/server-filesystem",
    "${workspaceFolder}",
    "/Users/<USER>/Projects"
  ]
}
```

**Docker版本（額外安全隔離）：**
```json
{
  "name": "filesystem-docker",
  "command": "docker",
  "args": [
    "run", "-i", "--rm",
    "--mount", "type=bind,src=/Users/<USER>/Desktop,dst=/projects/Desktop",
    "--mount", "type=bind,src=/Users/<USER>/Documents,dst=/projects/Documents,ro",
    "mcp/filesystem",
    "/projects"
  ]
}
```

**其他推薦伺服器：**

| 伺服器類型 | 設定範例 | 用途 |
|------------|----------|------|
| **GitHub官方** | `"command": "npx", "args": ["@modelcontextprotocol/server-github"]` | Repository管理、PR、Issues |
| **PostgreSQL** | `"command": "npx", "args": ["@modelcontextprotocol/server-postgres"]` | 資料庫查詢分析 |
| **SQLite** | `"command": "uvx", "args": ["mcp-server-sqlite", "--db-path", "/path/to/db"]` | 本地資料庫操作 |

#### **安全最佳實踐**

> [!danger] 重要安全考量
> MCP存在多種安全風險，必須謹慎配置：

**核心安全威脅：**
- **工具投毒**：惡意行為嵌入看似無害的工具
- **地毯式攻擊**：工具安裝後悄悄改變行為
- **間接提示詞注入**：攻擊者在訊息中隱藏惡意指令
- **憑證洩露**：MCP伺服器儲存的OAuth token被竊取
- **指令注入**：基礎的系統指令注入漏洞

**防護措施：**
1. **僅使用可信來源**：優先選擇官方或知名企業維護的MCP伺服器
2. **最小權限原則**：
   ```json
   // ✅ 正確：僅授權特定目錄
   "args": ["/Users/<USER>/Projects", "/Users/<USER>/Documents"]
   
   // ❌ 錯誤：避免授權根目錄
   "args": ["/", "/System"]
   ```
3. **使用Docker隔離**：提供額外的安全邊界
4. **啟用唯讀模式**：敏感目錄使用`,ro`標誌
5. **定期審查**：檢查工具描述變更和異常存取

#### **故障排解**

<details>
<summary><b>🔧 常見問題解決</b></summary>

**Q: MCP伺服器無法連接？**
- 檢查網路連接和指令路徑
- 驗證JSON語法正確性
- 確認必要依賴已安裝（Node.js、Docker等）

**Q: 檔案存取被拒絕？**
- 確認目錄路徑正確且具備存取權限
- 檢查路徑是否在授權目錄清單內
- 避免使用相對路徑，使用絕對路徑

**Q: Docker版本執行失敗？**
- 確認Docker daemon正在運行
- 檢查掛載路徑語法：`src=主機路徑,dst=容器路徑`
- 驗證影像可用：`docker pull mcp/filesystem`

</details>

> [!warning] 安全提醒
> - MCP伺服器將以您的用戶帳戶權限執行，請謹慎授權目錄
> - 定期備份重要檔案，避免意外刪除或修改
> - 監控MCP伺服器的存取日誌，及時發現異常行為
> - 遵循人工審核原則：重要操作應要求明確確認

### 外部服務整合

**支援的整合服務：**

| 服務 | 功能 | 範例用途 |
|------|------|----------|
| **🐙 GitHub** | Issue管理、PR建立、CI狀態查詢 | "實現Issue #123並開啟PR" |
| **📋 Linear** | 票券讀取更新、狀態管理 | "修復TES-1" |
| **🎫 Jira** | 票券操作、狀態更新 | "為這個bug建立Jira票券" |
| **📖 Confluence** | 文檔查詢更新 | "更新onboarding文檔" |
| **📝 Notion** | 知識庫搜尋（唯讀） | "查找API文檔的技術規格" |
| **🔍 Glean** | 企業內部搜尋（早期存取） | "搜尋過往類似事件解決方案" |

**設定步驟：**
1. 在Augment設定面板點擊對應服務的"Connect"
2. 完成OAuth授權流程
3. 授權後即可在Agent中使用整合功能

### Guidelines自訂規則

> [!abstract] Guidelines概述
> 透過自然語言指令來改善Agent和Chat的回應品質，使其符合您的偏好、最佳實踐和技術堆疊。

**User Guidelines（使用者規則）：**
- 套用於所有編輯器中的未來對話
- 透過Context選單或@提及新增
- 範例：
  - "For TypeScript code, explain what the code is doing in more detail"
  - "Respond to questions in Spanish"

**Workspace Guidelines（工作區規則）：**
- 在專案根目錄建立`.augment-guidelines`文件
- 套用於該代碼庫的所有Agent和Chat會話
- 範例內容：
```
- Use pytest instead of unittest for Python testing
- For NextJS, use the App Router and server components
- Avoid using the deprecated internal auth module
- Functions should start with verbs for naming consistency
```

> [!tip] 編寫Guidelines小技巧
> - 以列表形式提供規則
> - 使用簡潔明確的語言
> - 避免要求過短回應（可能影響品質）
> - 字元限制：最多24,576字元

### Remote Agent雲端代理

> [!abstract] Remote Agent概述
> 在安全的雲端環境中運行的Agent，可同時處理多個任務，並在VS Code中監控管理進度。

**Remote Agent vs 本地Agent差異：**
- ✅ **並行處理**：同一repo可同時運行多個代理
- ✅ **獨立環境**：每個代理有自己的workspace和分支
- ✅ **持續運行**：關閉VS Code後仍可繼續工作
- ✅ **SSH連接**：可直接連接代理環境進行編輯

**建立Remote Agent：**
1. 需要先連接GitHub帳戶
2. 選擇要處理的repository
3. 選擇分支或建立新分支
4. 選擇或建立運行環境
5. 輸入自然語言指令並建立代理

**環境自訂：**
- 使用基礎環境或bash腳本自訂
- 可安裝代理完成任務所需的工具
- 每個代理獨立的虛擬化作業系統

**SSH連接與編輯：**
- 需要安裝Remote-SSH擴展
- 從Remote Agent儀表板點擊SSH連接
- 可直接在雲端環境中查看編輯文件

---

## 🔧 實用技巧與最佳實踐

### 提示詞優化策略

> [!tip] 有效提示詞撰寫原則

**📝 詳細且具體**
- ❌ "修改這個函數"
- ✅ "將這個callback-based函數重構為使用async/await，並新增錯誤處理"

**🎯 提供context和約束**
- 指定使用的技術棧和版本
- 說明程式碼風格偏好
- 提及相關的專案規範

**📋 使用範例說明**
- 提供期望的輸入/輸出範例
- 說明預期的行為模式
- 展示相似的現有程式碼

**🔄 善用Enhance Prompt功能**
- 先撰寫簡短提示詞
- 點擊✨Enhance Prompt按鈕
- 審查並編輯增強後的提示詞

### Context管理技巧

**📁 檔案選擇策略：**
- 包含相關的介面定義和型別檔案
- 加入相似功能的現有實作參考
- 選擇測試檔案作為行為說明

**🔗 有效使用@提及：**
- `@package.json`：了解專案依賴
- `@README.md`：掌握專案概況
- `@docs/`：包含文檔資料夾
- `@Next.js`：引用官方文檔

**💬 對話管理：**
- 單一主題維持同一對話
- 變更主題時開啟新對話
- 利用對話歷史追蹤專案進度

### 常見問題解決

<details>
<summary><b>🔧 技術問題排解</b></summary>

**Q: Workspace索引失敗？**
- 檢查網路連接狀態
- 確認`.augmentignore`設定正確
- 嘗試重新開啟workspace

**Q: Agent執行時出錯？**
- 查看action詳細輸出
- 檢查終端指令是否正確
- 確認必要依賴已安裝

**Q: 代碼補全無法使用？**
- 確認自動補全未被關閉
- 檢查VS Code原生補全設定
- 重啟編輯器嘗試

**Q: MCP伺服器連接失敗？**
- 確認指令和參數正確
- 檢查必要依賴已安裝
- 查看Augment輸出記錄

</details>

---

## 🌟 CloudBase整合方案

> [!info] CloudBase AI Toolkit整合
> Augment Code與CloudBase AI Toolkit的整合方案，實現從想法到部署的完整全棧開發流程。

**🚀 5分鐘快速開始：**

**方式一：使用專案模板**
```bash
# 選擇預配置的專案模板
# 查看：https://github.com/TencentCloudBase/cloudbase-templates
```

**方式二：現有專案整合**
1. **設定MCP配置**
```json
{
  "augment.advanced": {
    "mcpServers": {
      "cloudbase": {
        "command": "npx",
        "args": ["@cloudbase/cloudbase-mcp@latest"]
      }
    }
  }
}
```

2. **下載AI規則**
```
在當前專案中下載雲開發AI規則
```

3. **開始使用**
```
登入雲開發
```

**💡 使用範例：**
- "建立一個線上投票系統，支援建立投票、參與投票、結果統計，使用雲資料庫儲存，最後部署"
- "為現有專案新增使用者認證功能，整合騰訊雲服務"
- "建立RESTful API並自動部署到CloudBase"

**🎯 核心優勢：**
- **⚡ 快速部署**：一鍵部署到騰訊雲開發
- **🇨🇳 國內最佳化**：CDN加速，Serverless架構
- **🔒 企業級安全**：330萬開發者驗證的平台

---

## 📚 相關資源

- **官方文檔**：[Augment Code Documentation](https://docs.augmentcode.com)
- **社群支援**：[GitHub Issues](https://github.com/augmentcode)
- **CloudBase整合**：[CloudBase AI Toolkit](https://github.com/TencentCloudBase/cloudbase-ai-toolkit)
- **MCP協議**：[Model Context Protocol](https://modelcontextprotocol.io)

---

## 版本控制

<details>
<summary>版本歷史</summary>
<b>版本 1.0</b> | 2025-06-22 | Claude AI<br>
- 初始版本<br>
- 完成完整功能文檔<br>
- 新增CloudBase整合說明<br>
</details>

當前版本：1.0  
最後更新：2025-06-22