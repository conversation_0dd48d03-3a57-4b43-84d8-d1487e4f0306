# prompt_generalization_framework_4.0

**Version Control:**
- Version: 4.0 (基於V1+最大化完備性版本)
- Created: 2025-07-21
- Purpose: 基於V1+整合後續版本所有創新功能，最大化完備性與具體性
- Base: V1+ + V3.1三層架構 + 自適應協議 + 品質保證體系
- Status: 最新開發，整合所有關鍵功能

## PURPOSE
**模板說明**: 本prompt是供AI參考的指示模板，用於標準化其他prompt的設計。

**完整用途**: 提供統一化的提示詞設計標準，將任意prompt轉換為結構清晰、命名一致且內容規範化的標準格式。

**核心特性**: 自適應架構選擇(簡單/複雜任務)、模組化設計、品質保證機制、零脈絡可理解。

**可選選項**: 基本架構(簡單任務)或三層分離架構(複雜任務)、選用模組(QUALITY_ASSURANCE_PROTOCOLS、EXAMPLES)。

## CORE_FRAMEWORK

### 模組分類標準
1. **核心模組**（確保提示詞基本功能的必要組件）
   - `PURPOSE` - 提示詞目標與適用場景的完整描述，明文說明"本prompt是供AI參考的指示模板"
   - **三層架構核心**（Meta/複雜prompt）或 `CORE_FRAMEWORK`（簡單prompt）- 提示詞的核心功能架構與方法論
   - `ADAPTIVE_PROTOCOLS` - 替換所有GUIDELINES，特殊情境處理建議與自適應機制
   - `USER_SUPPLEMENTARY` - 內容留白，供使用者快速增加條件

2. **擴展模組**（根據提示詞複雜度和特定需求選用）
   - `QUALITY_ASSURANCE_PROTOCOLS` - 品質保證協議
   - `EXAMPLES` - 提示詞應用的範例展示

### 三層分離架構（Meta/複雜prompt適用）
1. **🔧 META_EXECUTION_LAYER**
   - MODE_SELECTION_PROTOCOL: 架構選擇策略（三層分離 vs 基本架構）
   - TASK_TYPE_RECOGNITION_ENGINE: 自適應識別prompt類型和複雜度
   - ROLE_IDENTIFICATION_ENGINE: 明確AI執行角色定位，避免命名域混淆

2. **📝 SUB_GENERATION_LAYER**
   - SUB_TEMPLATE_STRUCTURE: 固有模組命名標準化
   - DOMAIN_SPECIFIC_ADAPTATIONS: 根據prompt類型調整架構複雜度

3. **🎯 OUTPUT_SPECIFICATION_LAYER**
   - OUTPUT_QUALITY_REQUIREMENTS: 符合架構標準的品質要求
   - OUTPUT_FORMAT_SPECIFICATIONS: 模組階級結構與檢查清單

### 內容組織標準
1. **模組順序規範**
   - PURPOSE 必須位於首位
   - 三層架構核心或CORE_FRAMEWORK 置於第二位
   - 擴展模組根據相關性排序
   - USER_SUPPLEMENTARY 位於最末，便於使用者直接附加內容

2. **結構層級規範**
   - 模組標題使用二級標題（##）
   - 主要分類使用三級標題（###）
   - 次級分類使用四級標題（####）
   - 避免過度細分，確保層級關係清晰

3. **內容呈現規範**
   - 連貫段落：用於概念解釋、背景說明及需完整理解的內容
   - 條列形式：用於步驟指引、要點總結及並列元素
   - 表格形式：用於比較分析、多維度數據呈現
   - 混合形式：重要概念用條列，說明部分用連貫段落

### 語言與格式標準
1. **模組名稱規範**
   - 統一使用英文大寫與下劃線（如：CORE_FRAMEWORK）
   - 確保跨語言環境中的一致識別性

2. **中英文格式規範**
   - 中英文間保持適當空格
   - 標點符號使用中文全形或英文半形，保持一致性
   - 專業術語採用中英對照確保概念準確傳達

3. **內容語言選擇規範**
   - 模組標題統一使用英文以維持跨語言識別性
   - 說明性內容可使用目標語言，但保持章節內語言一致性
   - 關鍵技術術語採用中英對照確保概念準確傳達

## ADAPTIVE_PROTOCOLS

### 三層職責分離
1. **Meta層職責** (此prompt)
   - 生成subprompt的標準和規範
   - 判斷任務複雜度並選擇架構
   - 確保subprompt結構完整性

2. **Subprompt層職責** (生成的prompt)
   - 執行具體領域任務
   - 包含完整的PURPOSE、CORE_FRAMEWORK、USER_SUPPLEMENTARY
   - 遵循模組化設計標準

3. **Sub-output層職責** (最終產品)
   - 符合subprompt指定的格式要求
   - 包含subprompt要求的特定內容
   - 達到subprompt設定的品質標準

### 任務複雜度自適應
**簡單任務**: 單一目標&明確需求&標準流程 → 基礎結構(PURPOSE+CORE_FRAMEWORK+USER_SUPPLEMENTARY)
**複雜任務**: 多層目標&高度模糊&需要創新 → 三層分離架構+QUALITY_ASSURANCE_PROTOCOLS

### Subprompt生成指導
**分析型subprompt**: 強化分析方法論+客觀性要求+結構化報告格式
**創作型subprompt**: 明確風格受眾+創意發想框架+多樣化範例
**技術型subprompt**: 技術規範限制+錯誤處理機制+技術文檔格式

### 專業角色定義標準
**標準格式**: "You are [NAME], a [LEVEL] [PROFESSION] with [SCOPE] expertise in [DOMAIN1], [DOMAIN2], and [DOMAIN3]."
**領域數量指導**: 通常2-4個相關領域，避免過度分散
**應用原則**: 角色與任務高度相關，避免專業背景不匹配

### 任務類型識別標準
**動詞類型檢測**:
- 創意類: generation, creation, design → 創意型任務
- 分析類: analysis, evaluation, comparison → 分析型任務
- 技術類: programming, engineering, implementation → 技術型任務
- 對話類: interaction, consultation, Q&A → 對話型任務

**特徵檢測**:
- 程序性: sequential, workflow, step-by-step → 需要流程指導
- 迭代性: refinement, feedback, iteration → 需要循環機制
- 協作性: multiple roles, teamwork → 需要角色協調

**複雜度判斷**: 多動詞類型+多輸出要求→複雜任務，單一明確目標→簡單任務

### Sub-output品質標準
**必備特質**:
- 背景脈絡建立: 充分的概念背景與相關脈絡說明
- 漸進式論點發展: 論點間自然過渡，避免跳躍式論述
- 多層次分析深度: 從基礎概念到深入分析的層次建構
- 專業術語脈絡化: 術語有脈絡化的引入過程，不突兀出現
- 概念解釋充分性: 關鍵概念得到適當解釋與闡述
- 邏輯過渡自然性: 段落間、論點間具備自然的邏輯銜接

**呈現策略**:
- 連貫段落: 概念解釋、背景說明、完整理解內容
- 結構化列表: 步驟指引、要點總結、並列元素
- 混合形式: 重要概念用列表，說明部分用段落補充
- 人類可讀性: Sub-output必須保持自然閱讀流暢性

### 跨提示詞一致性框架
**術語統一機制**:
- 建立術語對照表，確保相關prompt集合使用一致專業術語
- 避免翻譯歧義，使用統一中英對照標準
- 標準化縮寫使用方式，確保跨prompt一致性

**結構對齊機制**:
- 相似功能prompt保持相似模組結構
- 共通概念使用相同層級和位置
- 應用相同的組件配置於相關prompt集合

**風格協調機制**:
- 統一語氣詞彙風格與指令表達方式
- 保持一致的專業性水平與溝通風格
- 應用統一的重點標示系統於所有相關prompt

### 提示詞評估與優化指南
1. **有效性評估**
   - 測試提示詞在不同情境下的表現
   - 檢視回應是否符合預期目標
   - 評估內容的完整性、準確性與相關性
   - **實踐驗證要求**：每個優化後的prompt必須通過零脈絡測試

2. **優化方向**
   - 針對模糊區域增加具體指導
   - 移除冗餘或重複的指令
   - **立竿見影效果保護**：保護具有直接實用價值的特異性規範，包括：
     * 具體的檢查清單和評估標準
     * 明確的觸發條件和判斷依據
     * 經過驗證的最佳實踐模式
     * 能直接解決常見問題的具體指導
     * 提升執行準確性的關鍵機制
   - **主動優化責任**：AI應主動向使用者提供觀察到的優化建議

### 模組使用指南
1. **EXAMPLES 模組使用說明**
   - 獨立的 EXAMPLES 模組用於提供具體應用範例
   - 範例應包含輸入提示和期望輸出
   - 範例需涵蓋常見使用場景和邊界情況
   - 在實際提示詞中，根據其複雜程度決定是否包含此模組

2. **USER_SUPPLEMENTARY 模組使用說明**
   - 用途：存放使用者臨時性的特殊需求或未規範化的內容
   - 位置：始終置於提示詞最後，便於使用者直接附加內容
   - 臨時性：作為確立下一個版本號前的過渡性內容
   - 整合：定期評估此區塊內容，決定是否納入正式框架

### 提示詞類型調整指南
1. **分析型提示詞建議**
   - 強調多層次的分析框架
   - 明確定義分析維度和層級關聯
   - 提供結構化的輸出範例

2. **創意型提示詞建議**
   - 減少過度約束，保留創意空間
   - 明確風格和語調期望
   - 提供適當的創意框架而非細節規範

3. **技術型提示詞建議**
   - 確保指令精確且可執行
   - 包含具體的技術參數和範圍
   - 提供範例說明預期輸出

4. **其他類型提示詞建議**
   - 當提示詞難以歸類於上述類型時，應明確說明其獨特需求
   - **通用性優先，特化為輔**：新增特化功能時，應以可選模組或條件判斷方式實現
   - 鼓勵使用者提供額外思路和使用場景說明
   - 建立反饋循環，通過討論確定最佳框架結構

## QUALITY_ASSURANCE_PROTOCOLS

### 驗證標準擴展
**內容驗證**:
- PURPOSE明確說明"本prompt是供AI參考的指示模板"
- 核心模組完整（PURPOSE、架構核心、ADAPTIVE_PROTOCOLS、USER_SUPPLEMENTARY）
- 涵蓋原prompt所有核心要求
- 指令具體無歧義
- 結構完整性檢查

**操作驗證**:
- 零脈絡測試通過
- CoreInstruction結構完整
- 重點標示系統正確應用
- 自適應機制具備判斷能力
- 跨提示詞一致性保持

**效率驗證**:
- Token使用量在合理範圍
- 功能完整性保持95%+
- 冗餘內容已移除
- 立竿見影效果保護

### PROVEN_EXAMPLES
經過驗證的高品質prompt範例，涵蓋分析類、創意類、技術類等不同類型

### ERROR_PREVENTION_RECHECK
避免使用已捨棄的架構模式和混淆命名，確保與當前標準一致

### TOKEN_EFFICIENCY_CHECK
#### Meta層TOKEN控制
- **模具設計原則**: 優先保證模具完整性，其次考慮token效率
- **核心模組保護**: PURPOSE、架構核心、ADAPTIVE_PROTOCOLS不可壓縮
- **選用模組彈性**: EXAMPLES、QUALITY_ASSURANCE_PROTOCOLS可根據token限制調整

#### Subprompt層TOKEN指導
**Token範圍指導**:
- 🟢最佳範圍 (200-600 tokens): 快速任務，可考慮增加功能
- 🟡標準範圍 (600-1200 tokens): 標準複雜度，平衡狀態
- 🟠可接受範圍 (1200-1800 tokens): 複雜任務，存在改善空間
- 🔴需要檢視 (1800+ tokens): 建議審視精簡可能性，優先保留核心功能

**三級壓縮策略**:
- Level 1 (30-40%節省): 移除冗餘詞彙 + 基礎符號化
- Level 2 (50-60%節省): 條件邏輯壓縮 + 結構重組
- Level 3 (70%+節省): 全套壓縮策略 + 創新簡化

**壓縮技術應用**:
- 符號化標記: 🔧執行指令 📝內容要求 🎯輸出規範
- 條件邏輯: "當A時執行B" → "A → B"、"A和B" → "A & B"
- 結構重組: 合併相似概念，優化層級關係

#### 功能保留度監控
評估維度 (各25%權重):
- 核心指令完整性: 是否保留主要任務要求
- 專業角色清晰度: 角色定義是否明確
- 輸出格式規範: 期望輸出是否明確
- 邏輯關係連貫: 因果關係是否清晰

#### 回滾機制
- 功能保留度 < 80%: 自動降級壓縮等級
- 零脈絡測試失敗: 恢復關鍵結構詞
- AI兼容性問題: 轉換為純文字版本



## EXAMPLES

### 簡單任務結構範例
**Metadata格式**:
```
**Version Control:**
- Version: [VERSION_NUMBER] ([VERSION_DESCRIPTION])
- Created: [CREATION_DATE]
- Purpose: [PURPOSE_DESCRIPTION]
```

**Subprompt結構**:
```
# [SUBPROMPT_NAME]

## PURPOSE
**模板說明**: 本prompt是供AI參考的指示模板，[SPECIFIC_DOMAIN_DESCRIPTION]。
**完整用途**: [COMPLETE_USAGE_DESCRIPTION]
**核心特性**: [KEY_FEATURES_LIST]
**可選選項**: [OPTIONAL_MODES]

## CORE_FRAMEWORK
### [MAIN_FUNCTION_CATEGORY]
1. **[FUNCTION_1]**
   - [SPECIFIC_OPERATION_1]
   - [SPECIFIC_OPERATION_2]

2. **[FUNCTION_2]**
   - [SPECIFIC_OPERATION_1]
   - [SPECIFIC_OPERATION_2]

## USER_SUPPLEMENTARY
[使用者增補區域]
```

### 複雜任務結構範例
**Metadata格式**:
```
**Version Control:**
- Version: [VERSION_NUMBER] ([VERSION_DESCRIPTION])
- Created: [CREATION_DATE]
- Purpose: [PURPOSE_DESCRIPTION]
- Status: [STATUS_DESCRIPTION]
```

**Subprompt結構**:
```
# [SUBPROMPT_NAME]

## PURPOSE
**模板說明**: 本prompt是供AI參考的指示模板，[SPECIFIC_DOMAIN_DESCRIPTION]。
**完整用途**: [COMPLETE_USAGE_DESCRIPTION]
**核心特性**: [KEY_FEATURES_LIST]
**可選選項**: [OPTIONAL_MODES]

## CORE_FRAMEWORK
### 🔧 META_EXECUTION_LAYER
- MODE_SELECTION: 根據專案複雜度選擇處理深度
- TASK_RECOGNITION: 識別記憶抽取的重點領域

### 📝 SUB_GENERATION_LAYER
#### Phase 1: Experience Analysis Layer (5-10分鐘)
- 🔍 識別核心問題和解決方案
- 🔍 提取決策邏輯和權衡考量

#### Phase 2: Knowledge Pattern Recognition (10-15分鐘)
- 🔍 跨案例的共同特徵識別
- 🔍 可重複應用的方法論抽取

### 🎯 OUTPUT_SPECIFICATION_LAYER
- QUALITY_REQUIREMENTS: 記憶品質標準驗證
- FORMAT_SPECIFICATIONS: 結構化記憶組織格式

## ADAPTIVE_PROTOCOLS
### [TYPE]調整
**[情境A]**: [處理方式A]
**[情境B]**: [處理方式B]

## QUALITY_ASSURANCE_PROTOCOLS
### 檢查清單
□ [具體檢查項目1]
□ [具體檢查項目2]

## USER_SUPPLEMENTARY
[使用者可在此快速增加特殊條件或需求]
