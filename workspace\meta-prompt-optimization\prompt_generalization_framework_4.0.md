# prompt_generalization_framework_4.0

**Version Control:**
- Version: 4.0 (基於V1+最大化完備性版本)
- Created: 2025-07-21
- Purpose: 基於V1+整合後續版本所有創新功能，最大化完備性與具體性
- Base: V1+ + V3.1三層架構 + 自適應協議 + 品質保證體系

## PURPOSE
本prompt是供AI參考的指示模板，用於標準化其他prompt的設計。提供統一化的提示詞（prompt）設計標準，確保各類提示詞結構清晰、命名一致且內容規範化。本框架適用於創建高效能、可重複使用的提示詞，能跨平台應用且易於維護和改進。根據任務複雜度自動選擇適當架構(三層分離或基本架構)，確保結構清晰且功能完整，遵循所有品質保證標準。

## CORE_FRAMEWORK

### 模組分類標準
1. **核心模組**（確保提示詞基本功能的必要組件）
   - `PURPOSE` - 提示詞目標與適用場景的完整描述，明文說明"本prompt是供AI參考的指示模板"
   - **三層架構核心**（Meta/複雜prompt）或 `CORE_FRAMEWORK`（簡單prompt）- 提示詞的核心功能架構與方法論
   - `ADAPTIVE_PROTOCOLS` - 替換所有GUIDELINES，特殊情境處理建議與自適應機制
   - `USER_SUPPLEMENTARY` - 內容留白，供使用者快速增加條件

2. **擴展模組**（根據提示詞複雜度和特定需求選用）
   - `QUALITY_ASSURANCE_PROTOCOLS` - 品質保證協議
   - `PROCESSING_GUIDELINES` - 特殊情境處理建議（與ADAPTIVE_PROTOCOLS並存時使用）
   - `EXAMPLES` - 提示詞應用的範例展示
   - `VERSION_CONTROL` - 版本資訊與變更記錄

### 三層分離架構（Meta/複雜prompt適用）
1. **🔧 META_EXECUTION_LAYER**
   - MODE_SELECTION_PROTOCOL: 架構選擇策略（三層分離 vs 基本架構）
   - TASK_TYPE_RECOGNITION_ENGINE: 自適應識別prompt類型和複雜度
   - ROLE_IDENTIFICATION_ENGINE: 明確AI執行角色定位，避免命名域混淆

2. **📝 SUB_GENERATION_LAYER**
   - SUB_TEMPLATE_STRUCTURE: 固有模組命名標準化
   - DOMAIN_SPECIFIC_ADAPTATIONS: 根據prompt類型調整架構複雜度

3. **🎯 OUTPUT_SPECIFICATION_LAYER**
   - OUTPUT_QUALITY_REQUIREMENTS: 符合架構標準的品質要求
   - OUTPUT_FORMAT_SPECIFICATIONS: 模組階級結構與檢查清單

### 內容組織標準
1. **模組順序規範**
   - PURPOSE 必須位於首位
   - 三層架構核心或CORE_FRAMEWORK 置於第二位
   - 擴展模組根據相關性排序
   - USER_SUPPLEMENTARY 位於最末，便於使用者直接附加內容

2. **結構層級規範**
   - 模組標題使用二級標題（##）
   - 主要分類使用三級標題（###）
   - 次級分類使用四級標題（####）
   - 避免過度細分，確保層級關係清晰

3. **內容呈現規範**
   - 連貫段落：用於概念解釋、背景說明及需完整理解的內容
   - 條列形式：用於步驟指引、要點總結及並列元素
   - 表格形式：用於比較分析、多維度數據呈現
   - 混合形式：重要概念用條列，說明部分用連貫段落

### 語言與格式標準
1. **模組名稱規範**
   - 統一使用英文大寫與下劃線（如：CORE_FRAMEWORK）
   - 確保跨語言環境中的一致識別性

2. **中英文格式規範**
   - 中英文間保持適當空格
   - 標點符號使用中文全形或英文半形，保持一致性
   - 專業術語採用中英對照確保概念準確傳達

3. **內容語言選擇規範**
   - 模組標題統一使用英文以維持跨語言識別性
   - 說明性內容可使用目標語言，但保持章節內語言一致性
   - 關鍵技術術語採用中英對照確保概念準確傳達

## ADAPTIVE_PROTOCOLS

### 任務複雜度自適應
1. **簡單任務**
   - 觸發條件：單一目標、明確需求、標準流程
   - 自動選擇：基礎結構 + 核心功能
   - 執行策略：直接處理，最小化複雜度

2. **中等任務**
   - 觸發條件：多重目標、部分模糊需求、需要判斷
   - 自動選擇：標準結構 + 擴展功能
   - 執行策略：分步處理，適度複雜度

3. **複雜任務**
   - 觸發條件：多層目標、高度模糊、需要創新
   - 自動選擇：完整結構 + 高級功能（三層分離架構）
   - 執行策略：深度分析，最大化功能

### 提示詞類型調整指南
1. **分析型提示詞建議**
   - 強化核心架構中的分析方法論
   - 在ADAPTIVE_PROTOCOLS中加入客觀性要求
   - 需明確分析報告格式

2. **創作型提示詞建議**
   - PURPOSE中明確創作風格與目標受眾
   - 核心架構包含創意發想與結構規劃
   - 可在EXAMPLES中提供多樣化創作範例

3. **技術型提示詞建議**
   - 核心架構需包含技術規範與限制條件
   - ADAPTIVE_PROTOCOLS加入錯誤處理機制
   - 明確技術文檔格式

### 跨提示詞一致性指南
1. **術語統一**
   - 在相關提示詞集合中使用一致的專業術語
   - 建立術語表確保翻譯和解釋的一致性
   - 統一縮寫和簡稱的使用方式

2. **結構對齊**
   - 相似功能的提示詞應保持相似的模組結構
   - 確保共通概念在不同提示詞中使用相同的層級和位置
   - 採用一致的格式化標準

3. **風格協調**
   - 保持語氣和詞彙風格的一致性
   - 統一指令和建議的表達方式
   - 在所有提示詞中保持相同的專業性水平

### 提示詞評估與優化指南
1. **有效性評估**
   - 測試提示詞在不同情境下的表現
   - 檢視回應是否符合預期目標
   - 評估內容的完整性、準確性與相關性
   - **實踐驗證要求**：每個優化後的prompt必須通過零脈絡測試

2. **優化方向**
   - 針對模糊區域增加具體指導
   - 移除冗餘或重複的指令
   - **立竿見影效果保護**：任何模組或內容若具有立竿見影的實用效果，不得僅為精簡而移除
   - **主動優化責任**：AI應主動向使用者提供觀察到的優化建議

## PROCESSING_GUIDELINES

### 模組使用指南
1. **EXAMPLES 模組使用說明**
   - 獨立的 EXAMPLES 模組用於提供具體應用範例
   - 範例應包含輸入提示和期望輸出
   - 範例需涵蓋常見使用場景和邊界情況
   - 在實際提示詞中，根據其複雜程度決定是否包含此模組

2. **USER_SUPPLEMENTARY 模組使用說明**
   - 用途：存放使用者臨時性的特殊需求或未規範化的內容
   - 位置：始終置於提示詞最後，便於使用者直接附加內容
   - 臨時性：作為確立下一個版本號前的過渡性內容
   - 整合：定期評估此區塊內容，決定是否納入正式框架

### 提示詞類型調整指南
1. **分析型提示詞建議**
   - 強調多層次的分析框架
   - 明確定義分析維度和層級關聯
   - 提供結構化的輸出範例

2. **創意型提示詞建議**
   - 減少過度約束，保留創意空間
   - 明確風格和語調期望
   - 提供適當的創意框架而非細節規範

3. **技術型提示詞建議**
   - 確保指令精確且可執行
   - 包含具體的技術參數和範圍
   - 提供範例說明預期輸出

4. **其他類型提示詞建議**
   - 當提示詞難以歸類於上述類型時，應明確說明其獨特需求
   - **通用性優先，特化為輔**：新增特化功能時，應以可選模組或條件判斷方式實現
   - 鼓勵使用者提供額外思路和使用場景說明
   - 建立反饋循環，通過討論確定最佳框架結構

### 常用變數指南
1. **核心變數**
   - **[PROMPT]**：使用者的主要輸入內容
   - **[TARGETLANGUAGE]**：指定回應語言
   - **[QUERY]**：使用者的特定問題

2. **輔助變數**
   - **[CONTEXT]**：提供背景或參考資訊
   - **[FORMAT]**：指定輸出格式（如Markdown、JSON等）
   - **[LENGTH]**：指定回應的預期長度

## QUALITY_ASSURANCE_PROTOCOLS

### CHECKLISTS
1. **基礎檢查清單**
   - PURPOSE是否明確說明"本prompt是供AI參考的指示模板"
   - 核心模組是否完整（PURPOSE、架構核心、ADAPTIVE_PROTOCOLS、USER_SUPPLEMENTARY）
   - 模組命名是否使用UPPERCASE_WITH_UNDERSCORES格式

2. **架構檢查清單**
   - 是否正確選擇三層分離架構或基本架構
   - 自適應機制是否具備判斷能力
   - 跨提示詞一致性是否保持

3. **品質檢查清單**
   - 零脈絡測試是否通過
   - 立竿見影效果是否保護
   - TOKEN_EFFICIENCY_CHECK是否平衡功能完整性與簡潔性

### PROVEN_EXAMPLES
經過驗證的高品質prompt範例，涵蓋分析類、創意類、技術類等不同類型

### ERROR_PREVENTION_RECHECK
避免使用已捨棄的架構模式和混淆命名，確保與當前標準一致

### TOKEN_EFFICIENCY_CHECK
監控token使用效率，在保持功能完整性的前提下優化簡潔性

## EXAMPLES

### 基本應用範例
```
輸入：創建一個社交媒體內容生成的prompt
輸出：基於框架標準化的完整prompt，包含角色定位、流程描述和自然語言指令
```

### 複雜應用範例
```
輸入：多領域知識整合的分析型prompt
輸出：採用三層分離架構的結構化分析框架，包含多層次分析維度和跨領域整合指導
```

## USER_SUPPLEMENTARY
[使用者可在此快速增加特殊條件或需求]
