# enhanced_prompt_generalization_framework_2.0

**Version Control:**
- Version: 2.0 (統合版本：基於4.0 > 4.2路徑，注重完備性)
- Created: 2025-06-25
- Purpose: 為AI提供規格化的提示詞設計標準，確保結構清晰、命名一致且內容規範化，創建高效能、可重複使用的提示詞
- Base: v4.2完備性設計，整合v4.0核心架構

````
# enhanced_prompt_generalization_framework

## PURPOSE
為AI提供規格化的提示詞設計標準，確保結構清晰、命名一致且內容規範化，創建高效能、可重複使用的提示詞。

## CORE_FRAMEWORK

### StructuralLevel (結構層級)
CoreInstruction必須結構:
- PURPOSE: 明確功能定位與適用範圍
- CORE_FRAMEWORK: 核心功能架構與方法論
- OUTPUT_SPECIFICATIONS: 執行指令與回應規範

### FunctionalLevel (功能層級)  
StandardComponents四大組件庫:
- ProcessingGuidelines: 特殊情境處理建議
- Examples: 應用範例展示
- VersionControl: 版本追蹤與更新記錄
- UserSupplementary: 使用者增補資訊

### ImplementationLevel (實施層級)
動詞+對象標示系統:
- 分析+內容 → ContentAnalysis
- 生成+回應 → ResponseGeneration  
- 驗證+品質 → QualityVerification

### EmphasisSystem
```
emphasisHierarchy = {
  critical: "{{CRITICAL}} [不可違反內容]",
  directive: "**VERB** [具體執行內容]", 
  note: "__NOTE__ [注意事項]",
  technical: "`technical_term`",
  conditional: "??[完整條件判斷語句]??",
  checkpoint: "@@[完整驗證要求語句]@@",
  aiComment: "//AI備註內容,不輸出至最終結果//"
}
```

### LogicalOperationSystem
```
operators = {
  basicLogic: "& (and) | || (or) | → (implication) | ¬ (not)",
  setOperations: "∈ (belongs to) | ≡ (equivalent)",
  applicationContext: "??condition?? → action | constraint ∈ scope"
}

usageGuideline = {
  metaSubLevel: "**APPLY** 完整符號系統(→, &, ||, ∈, ¬, ≡)簡化邏輯表達",
  subOutputLevel: "**LIMIT** 僅使用→(因果關係) & &(並列關係)",
  prohibitedInOutput: "||, ∈, ¬, ≡等符號 **AVOID** 在sub-output中使用"
}
```

### FrameworkDefinition
```
frameworkLevels = {
  structuralLevel: "結構層 (StructuralModule) //框架主要結構,固定命名//",
  functionalLevel: "功能層 (FunctionalComponent) //用以包裹或分類複數個實現單元//", 
  implementationLevel: "實現層 (ImplementationLevel) //以功能分類包裹複數個釋義單元//",
  defUnit: "最小釋義單元 (DefinitionUnit)"
}

namingConvention = {
  structuralLevel: "UPPERCASE_WITH_UNDERSCORES (## level)",
  functionalLevel: "PascalCase (### level)", 
  implementationLevel: "PascalCase (no # marker)",
  defUnit: "camelCase"
}
```

### FiniteModulesDefinition
```
coreModules = {
  PURPOSE: "目標概述層",
  CORE_FRAMEWORK: "核心功能架構",
  USER_SUPPLEMENTARY: "用戶臨時補充區域"
}

extensionModules = {
  PROCESSING_GUIDELINES: "特殊情境處理建議", 
  EXAMPLES: "範例展示+對照模板",
  VERSION_CONTROL: "版本追蹤與更新記錄"
}
```

## PROCESSING_GUIDELINES

### Cross-Prompt Consistency
術語統一標準: 相同概念在不同prompt中使用一致術語
格式統一標準: 變數格式、模組命名、結構層級保持一致
邏輯統一標準: 處理流程、決策機制、品質標準統一

### Quality Control Mechanisms
{{CRITICAL}} 立竿見影效果保護: 確保核心功能不受優化影響
==零脈絡測試==通過: prompt在無額外說明下能獨立運作
__專業術語適度使用__: 平衡專業性與可理解性

### Adaptive Processing
```
taskComplexity ∈ {simple, medium, complex}
simple → 基礎結構 + 核心功能
medium → 標準結構 + 擴展功能  
complex → 完整結構 + 高級功能
```

### Optimization Strategies
```
tokenEfficiency = {
  redundancyElimination: "移除重複或冗餘描述",
  precisionEnhancement: "提升指令精確度",
  structureOptimization: "優化模組組織結構"
}

qualityAssurance = {
  semanticAccuracy: "確保語義準確性",
  functionalCompleteness: "保證功能完整性", 
  crossPlatformCompatibility: "維持跨平台兼容性"
}
```

## EXAMPLES

### 標準化範例
```
輸入: 基礎prompt需要結構化
處理: 應用框架標準 → 模組化組織 → 品質驗證
輸出: 符合標準的結構化prompt
```

### 複雜優化範例
```
輸入: 複雜多功能prompt需要優化
處理: 分析功能模組 → 應用適應性協議 → 整合優化策略
輸出: 高效能的標準化prompt
```

## OUTPUT_SPECIFICATIONS
"The target language is [TARGETLANGUAGE]. Apply enhanced generalization framework to: [PROMPT]"

## USER_SUPPLEMENTARY
// 此區域用於使用者增補臨時性需求或內容
// 在確立下一個版本號前作為過渡使用
// 請在此直接添加您的特殊要求
````
