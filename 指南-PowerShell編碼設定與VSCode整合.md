---
title: 指南-PowerShell編碼設定與VSCode整合
created: 2025-06-22
modified: 2025-06-25
version: "1.1"
tags: [主題/技術/程式設計, 類型/指南, 狀態/已完成, 備註/AI生成]
aliases: [PowerShell編碼, VSCode中文設定, 編碼相容性]
---

> [!note] 彙整者備註
>

## TL;DR 快速摘要

針對 Windows 10/11 繁體中文系統下 PowerShell 5.1 + VS Code + 多語言開發環境的編碼統一解決方案。重點：分語言設定編碼（PowerShell 用 UTF-8 BOM，Python/JS 用 UTF-8），建立 PowerShell Profile 管理編碼切換，避免全域設定影響傳統程式相容性。

## 章節樹快速導航

- **[[#問題背景與目標]]**
- **[[#核心解決策略]]**
    ├─── [[#分語言編碼設定原則]]
    └─── [[#避免全域設定風險]]
- **[[#PowerShell Profile 設定]]**
    ├─── [[#基礎編碼配置]]
    ├─── [[#快速切換函數]]
    └─── [[#WSL2 相容性]]
- **[[#VS Code 配置優化]]**
    ├─── [[#分語言編碼設定]]
    ├─── [[#終端機配置]]
    └─── [[#Augment Code 相容性]]
- **[[#實用工具模組]]**
    ├─── [[#編碼檢測工具]]
    └─── [[#批次轉換功能]]
- **[[#實施與驗證]]**

---

## 🎯 問題背景與目標

### 環境描述
- **作業系統**：Windows 10/11 繁體中文（預設編碼 950）
- **PowerShell**：5.1 版本（預設 Windows-1252）
- **開發需求**：PowerShell + Python + JavaScript
- **工具鏈**：VS Code + Augment Code 插件 + WSL2

### 核心挑戰
- PowerShell 5.1 腳本檔案需要 UTF-8 BOM 才能正確解析中文
- VS Code 預設 UTF-8 without BOM 與 PowerShell 不相容
- 全域 UTF-8 設定可能影響傳統 Windows 程式
- AI 插件間可能存在編碼處理衝突

---

## 📋 核心解決策略

### 分語言編碼設定原則

> [!tip] 最佳實踐
> 根據語言特性分別設定編碼，避免一刀切造成的相容性問題

| 語言 | 編碼設定 | 原因 |
|------|----------|------|
| PowerShell | UTF-8 with BOM | PowerShell 5.1 無 BOM 時預設 Windows-1252 |
| Python | UTF-8 without BOM | PEP 263 建議，跨平台相容 |
| JavaScript | UTF-8 without BOM | Web 標準，現代瀏覽器預設 |

### 避免全域設定風險

**不建議使用 `chcp 65001` 的原因**：
- 影響傳統 Windows 程式（記事本、某些 CMD 工具）
- 可能導致既有腳本執行錯誤
- PowerShell Profile 方案更精確可控

---

## ⚙️ PowerShell Profile 設定

### 基礎編碼配置

```powershell
# === 編碼物件建立 ===
$utf8WithBom = New-Object System.Text.UTF8Encoding $true
$utf8NoBom = New-Object System.Text.UTF8Encoding $false

# === 預設編碼設定 ===
[Console]::InputEncoding = $utf8WithBom
[Console]::OutputEncoding = $utf8WithBom
$OutputEncoding = $utf8WithBom
```

### 快速切換函數

```powershell
function Switch-ToNoBOM {
    [Console]::OutputEncoding = $script:utf8NoBom
    $global:OutputEncoding = $script:utf8NoBom
    Write-Host "切換至 UTF-8 no BOM (適用 WSL2)" -ForegroundColor Yellow
}

function Switch-ToBOM {
    [Console]::OutputEncoding = $script:utf8WithBom
    $global:OutputEncoding = $script:utf8WithBom
    Write-Host "切換至 UTF-8 with BOM (預設)" -ForegroundColor Green
}
```

### WSL2 相容性

```powershell
function wsl-cmd {
    param([string]$Command)
    Switch-ToNoBOM
    try { wsl $Command } finally { Switch-ToBOM }
}
```

---

## 🔧 VS Code 配置優化

### 分語言編碼設定

```json
{
  "files.autoGuessEncoding": true,

  "[powershell]": {
    "files.encoding": "utf8bom",
    "files.eol": "\r\n"
  },

  "[python]": {
    "files.encoding": "utf8",
    "files.eol": "\n"
  },

  "[javascript]": {
    "files.encoding": "utf8",
    "files.eol": "\n"
  }
}
```

### 終端機配置

**建議的終端機 Profile**：
```json
"terminal.integrated.profiles.windows": {
  "PowerShell 5.1 (Standard)": {
    "path": "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
    "args": [],
    "icon": "terminal-powershell"
  }
}
```

### Augment Code 相容性

> [!warning] 已知問題
> 安裝多個 AI 插件可能導致功能衝突，建議監控並適時停用衝突插件

```json
{
  "augment.enabledLanguages": ["powershell", "python", "javascript"],
  "github.copilot.enable": {
    "*": true,
    "plaintext": false
  }
}
```

---

## 🛠️ 實用工具模組

### 編碼檢測工具

```powershell
function Test-FileEncoding {
    param([string]$Path)
    $bytes = [System.IO.File]::ReadAllBytes($Path)
    if ($bytes.Length -ge 3 -and $bytes[0] -eq 0xEF -and $bytes[1] -eq 0xBB -and $bytes[2] -eq 0xBF) {
        return "UTF-8 with BOM"
    } elseif ($bytes.Length -ge 2 -and $bytes[0] -eq 0xFF -and $bytes[1] -eq 0xFE) {
        return "UTF-16 LE"
    } else {
        return "UTF-8 without BOM or other"
    }
}
```

### 批次轉換功能

```powershell
function Convert-PSFileEncoding {
    param([string]$Path, [switch]$Recursive)

    $files = if ($Recursive) {
        Get-ChildItem $Path -Filter "*.ps1" -Recurse
    } else {
        Get-ChildItem $Path -Filter "*.ps1"
    }

    foreach ($file in $files) {
        $content = Get-Content $file.FullName -Raw
        Set-Content $file.FullName -Value $content -Encoding UTF8
        Write-Host "轉換完成: $($file.Name)" -ForegroundColor Green
    }
}
```

---

## ✅ 實施與驗證

### 實施步驟

1. **建立 PowerShell Profile**
   ```powershell
   if (!(Test-Path $PROFILE)) { New-Item -Path $PROFILE -Type File -Force }
   ```

2. **套用 VS Code 設定**
   - 更新 `settings.json`
   - 重啟 VS Code

3. **測試驗證**
   ```powershell
   # 建立包含中文的測試檔案
   "測試中文字元 # Test Chinese" | Out-File test.ps1 -Encoding UTF8
   Test-FileEncoding .\test.ps1
   ```

### 驗證檢查清單

- [ ] PowerShell 腳本中文字元正常顯示
- [ ] Python/JS 檔案 UTF-8 格式正確
- [ ] WSL2 互動無亂碼
- [ ] Augment Code 功能正常
- [ ] 傳統程式未受影響

> [!success] 預期效果
> 達成多語言開發環境的編碼統一，保持系統相容性，提升開發效率

---

## 🔗 相關資源

- [Microsoft PowerShell 編碼文檔](https://docs.microsoft.com/powershell/scripting/components/vscode/understanding-file-encoding)
- [VS Code 編碼設定指南](https://code.visualstudio.com/docs/editor/codebasics#_file-encoding-support)
