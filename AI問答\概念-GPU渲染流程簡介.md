---
title: 概念-GPU渲染流程簡介
created: 2025-06-25
modified: 2025-06-25
version: "1.0"
tags: [主題/技術/圖形學, 主題/技術/GPU, 類型/概念, 狀態/已完成, 備註/AI生成]
aliases: [GPU渲染, 圖形渲染管線, 3D渲染流程]
---

# 概念-GPU渲染流程簡介

> [!note] 彙整者備註:
>

> [!note] 基本框架 GPU 渲染管線將 3D 場景轉換為 2D 螢幕影像，整個過程包含多個階段，每個階段負責特定任務。與 CPU 的循序處理不同，GPU 設計用於大量資料的**平行處理**。

## 🔄 座標轉換流程

理解 GPU 程式設計的關鍵是掌握座標如何在不同空間中轉換：

|空間|目的|範例|
|---|---|---|
|**局部空間**|定義物體本身的形狀|一個立方體的頂點相對於其中心定義|
|**世界空間**|放置物體到場景中|立方體位於場景中的特定位置|
|**視圖空間**|從相機視角看世界|物體相對於相機的位置|
|**裁剪空間**|確定視野範圍內的物體|超出視錐體的物體被裁剪|
|**螢幕空間**|映射到最終像素位置|3D 點轉換為螢幕上的 2D 座標|

> [!tip] 理解類比 想像拍攝一張照片：**局部空間**是物體自身形狀，**世界空間**是物體在現實世界的位置，**視圖空間**是從相機視角看到的景象，**裁剪空間**是決定哪些物體在照片範圍內，**螢幕空間**是最終照片上的 2D 像素。

## 🔃 GPU 渲染管線主要階段

```
輸入資料 → 頂點處理 → 圖元裝配 → 光柵化 → 片段處理 → 輸出合併 → 螢幕顯示
```

1. **頂點著色器**（可程式化）

    - 處理每個頂點的**位置轉換**和屬性計算
    - 輸入：原始頂點資料（位置、顏色、法線等）
    - 輸出：轉換後的頂點位置及其他屬性
2. **光柵化**（硬體固定功能）

    - 將三角形轉換為**像素片段**（未確定最終顏色的像素）
    - 確定哪些像素被三角形覆蓋
    - 將頂點屬性**插值**到覆蓋的每個像素
3. **片段著色器**（可程式化）

    - 為每個像素計算**最終顏色**
    - 執行紋理採樣、光照計算等
    - 決定每個像素的視覺外觀

> [!warning] 重要區別 **渲染**是整個將 3D 模型轉換為 2D 影像的完整過程。 **光柵化**只是其中一個步驟，負責將三角形轉換為像素集合。

## 📦 記憶體與緩衝區

GPU 程式設計中的主要資料緩衝區：

|緩衝區|用途|存取方式|
|---|---|---|
|**頂點緩衝區**|存儲頂點資料|頂點著色器讀取|
|**索引緩衝區**|定義三角形連接方式|圖元裝配階段使用|
|**紋理記憶體**|存儲圖像資料|片段著色器採樣|
|**幀緩衝區**|存儲最終畫面|輸出合併階段寫入|
|**深度緩衝區**|存儲像素深度值|用於深度測試|

## 💡 GPU 程式設計模型

```
CPU 準備資料 → 上傳資料到 GPU → 設定著色器與狀態 → 繪製指令 → GPU 執行渲染
```

與傳統程式設計不同，GPU 程式設計採用**狀態機模型**：

1. **著色器程式**：您編寫的核心程式碼，分為頂點著色器和片段著色器
2. **資料上傳**：將頂點資料、紋理等傳送到 GPU 記憶體
3. **渲染狀態**：設定混合模式、深度測試等
4. **繪製呼叫**：觸發 GPU 執行渲染

## 🔄 CPU 與 GPU 架構對比

|特性|CPU|GPU|
|---|---|---|
|**核心數量**|較少（通常 4-64 個）|極多（數百至數千個）|
|**單核效能**|高，優化執行序列指令|低，優化大量簡單平行任務|
|**適用工作**|複雜邏輯、分支預測|相同指令應用於大量資料|

> [!note] CPU 確實支援並行處理（多核心、SIMD 指令集如 AVX 等），但 GPU 被設計為「大規模並行處理器」，專為特定類型的計算優化。

GPU 渲染管線的獨特之處在於其**資料流性質**和**專門化階段**，而不僅僅是並行執行。每個階段都針對特定圖形任務進行了優化，例如頂點轉換、片段著色等。

---

## 📝 小結

- GPU 設計用於**平行處理大量資料**，特別適合圖形渲染
- 渲染管線將 3D 資料轉換為 2D 影像，經過多個座標空間轉換
- 主要可程式化階段是**頂點著色器**和**片段著色器**
- GPU 程式設計遵循**狀態機模型**而非傳統的指令循序模型
- 理解緩衝區管理和座標轉換是掌握 GPU 程式設計的關鍵

> [!tip] 對程式設計師的建議 開始學習 GPU 程式設計時，專注於理解頂點和片段著色器的基本功能，以及資料如何在這些階段之間流動。逐步深入更複雜的技術和優化方法。
