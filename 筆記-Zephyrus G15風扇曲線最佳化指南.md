---
title: Zephyrus G15散熱優化與風扇曲線設計
created: 2025-03-05
modified: 2025-05-16
version: "1.0"
tags: [硬體/散熱, 硬體/風扇曲線, 硬體/Zephyrus G15, 技術/調校, 主題/溫度管理]
aliases: [G15風扇優化, 溫度調校, 筆電散熱]
---

# 🌡️ Zephyrus G15散熱優化與風扇曲線設計

## 🔥 關鍵溫度閾值

> [!info] 設計背景 了解硬體溫度特性是風扇曲線優化的基礎。以下閾值基於硬體規格與實際使用經驗整理，作為風扇曲線設計的關鍵參考點。

### GPU (NVIDIA RTX 3060) 關鍵溫度點

- **70°C** - 非預期降頻風險點：部分使用者報告在此溫度時，功耗可能從80W驟降至30W
- **87°C** - NVIDIA官方最大運作溫度：達到此溫度會開始軟體降頻控制
- **102°C** - 硬體強制降頻保護點：極少達到，為硬體最後防線

### CPU (AMD Ryzen 6800HS) 關鍵溫度點

- **95-96°C** - CPU TjMax (最大結溫)：接近時會逐步降頻保護處理器
- **45W功耗點** - 效能/功耗效率拐點：超過此功耗增益有限

### 實際運作溫度範圍

- **安靜模式待機**: 49-59°C (室溫20°C環境下)
- **平衡模式工作**: 60-80°C (一般混合負載)
- **高性能模式**: 80-90°C (中高負載)

## 📊 優化風扇曲線

### 風扇曲線設定值

> [!warning] 實施提醒 這些值為十六進制格式，需要完整精確地輸入到G-Helper中。請確保每個字符都正確無誤，否則可能導致風扇行為異常。

|模式|風扇曲線設定|
|---|---|
|**安靜模式 (2)**|**CPU**: 1E-32-3C-46-50-57-5C-63-00-00-00-0F-19-23-28-32<br>**GPU**: 1E-32-3C-46-50-57-5C-63-00-00-0A-14-1E-28-2D-37|
|**平衡模式 (0)**|**CPU**: 1E-32-3C-46-50-57-5C-63-00-00-0A-19-28-37-41-4B<br>**GPU**: 1E-32-3C-46-50-57-5C-63-00-05-14-23-32-41-4B-55|
|**效能模式 (1)**|**CPU**: 1E-32-3C-46-50-57-5C-63-00-0A-1E-2D-3C-4B-5A-64<br>**GPU**: 1E-32-3C-46-50-57-5C-63-00-14-23-32-41-50-5F-64|

### 溫度點與風扇速度對照表

> [!note] 使用指引 此表格提供直觀的溫度與風扇速度對應關係，幫助理解風扇行為。百分比數值代表相對於最大轉速的比例。

|溫度|安靜模式 (CPU/GPU)|平衡模式 (CPU/GPU)|效能模式 (CPU/GPU)|
|---|---|---|---|
|30°C|0% / 0% (停轉)|0% / 0% (停轉)|0% / 0% (停轉)|
|50°C|0% / 0% (停轉)|0% / 5%|10% / 20%|
|60°C|0% / 10%|10% / 20%|30% / 35%|
|70°C|15% / 20%|25% / 35%|45% / 50%|
|80°C|25% / 30%|40% / 50%|60% / 65%|
|87°C|35% / 40%|55% / 65%|75% / 80%|
|92°C|40% / 45%|65% / 75%|90% / 95%|
|99°C|50% / 55%|75% / 85%|100% / 100%|

### 各模式特點

<details> <summary>安靜模式特點</summary>

- 優先低噪音設計，60°C以下幾乎不運轉風扇
- 在70°C後緩慢提升冷卻能力，避免頻繁波動
- 即使在高溫下也維持較低的風扇轉速，優先考慮安靜性
- 適合文書工作、網頁瀏覽、影音觀看等輕負載任務

</details> <details> <summary>平衡模式特點</summary>

- 在60-75°C區間採用寬間隔的風扇級別，減少頻繁波動
- 提供比實際需求高10-15%的冷卻能力，建立溫度緩衝
- 在70°C前積極提升風扇轉速，預防GPU非預期降頻
- 適合日常混合工作負載、輕度遊戲、內容創作

</details> <details> <summary>效能模式特點</summary>

- 從50°C開始積極冷卻，預防溫度迅速攀升
- 在70-87°C區間提供大量冷卻能力，確保穩定性能輸出
- 接近CPU/GPU熱節流點前達到最大風扇轉速
- 適合遊戲、影片渲染、3D建模等高負載任務

</details>

## 🔧 設計原則與考量

> [!tip] 設計理念 這些原則不僅適用於Zephyrus G15，也可作為其他筆電風扇曲線調校的基礎思路。關鍵在於平衡溫度控制、噪音水平和使用者體驗。

### 階梯式設計

採用階梯式設計而非線性漸進曲線的理由：

- **減少風扇波動**: 筆電風扇啟動和調整有10-30秒的延遲，階梯式設計可以減少風扇在臨界溫度點附近不斷調整的問題
- **穩定使用體驗**: 風扇速度在一定溫度範圍內保持不變，提供可預測的噪音水平
- **決定性強**: 明確的轉速階段不會因小幅溫度波動而頻繁變化，降低使用干擾

### 高餘裕策略

在預期工作溫度設定比理論需求高10-15%的風扇轉速：

- **建立溫度緩衝**: 創造溫度下降的趨勢，而非僅維持現狀
- **減少邊界徘徊**: 防止溫度在臨界點附近上下波動
- **降低調整頻率**: 系統不需要頻繁調整風扇速度來應對小幅溫度變化

### 關鍵溫度防護

特別針對重要溫度閾值提前防護：

- 在**70°C前**提供足夠冷卻，避免GPU非預期降頻
- 在**87°C前**達到較高冷卻能力，防止觸發GPU官方降頻機制
- 在接近**95°C**時提供最大冷卻，保護CPU不超過TjMax

## ⚡ 功耗設定

> [!important] 功耗與溫度關係 功耗限制是控制溫度的另一重要手段。適當限制功耗能顯著降低熱輸出，在保持合理性能的同時改善系統溫度特性。

### 建議功耗設定值

|功耗設定|安靜模式|平衡模式|效能模式|
|---|---|---|---|
|CPU Boost|Disabled|Efficient Enabled|Enabled (Aggressive)|
|SPL (持續功耗限制)|20W|35W|45W|
|sPPT (2分鐘提升)|25W|40W|50W|
|fPPT (2秒提升)|25W|45W|65W|
|GPU核心時脈限制|1280 MHz|1450 MHz|1580 MHz|
|核心時脈偏移量|+125 MHz|+125 MHz|+125 MHz|

> [!warning] 設定限制 軟體規定必須遵循 **SPL < sPPT < fPPT** 的限制

## 🛠️ 實施方法

### 在G-Helper中應用風扇曲線

1. 打開G-Helper應用程式
2. 進入風扇設定頁面
3. 將相應的風扇曲線值複製到對應模式的CPU和GPU欄位中
4. 儲存設定並測試效果

> [!tip] 實施建議 建議在實施新風扇曲線後，使用HWiNFO等監測工具觀察幾天的溫度趨勢，確保效果符合預期。

## 🔍 故障排除與進階調整

### 風扇波動問題

如果仍然觀察到風扇在某些溫度區間頻繁波動：

- 擴大該溫度區間的階梯寬度
- 考慮將相鄰溫度點的風扇轉速設為相同值
- 調整CPU功耗限制，降低溫度波動幅度

### 季節性調整建議

|環境條件|建議調整|
|---|---|
|**夏季**|降低各溫度點2-3°C，提前啟動冷卻|
|**冬季**|提高各溫度點2-3°C，延後風扇啟動|
|**高環境溫度**|提高風扇轉速基準值5-10%|

### 長期使用注意事項

- 定期清潔散熱系統，保持風扇和散熱片清潔
- 每3-6個月重新評估風扇曲線效果，視需要進行微調
- 使用硬體監控軟體(如HWiNFO)檢查長期溫度趨勢

> [!success] 效果評估 這套優化風扇曲線方案已針對Zephyrus G15的特性進行了量身定制，結合了階梯式設計與高餘裕策略，應該能提供絕佳的平衡使用體驗。
