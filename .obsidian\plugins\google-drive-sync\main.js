/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var V=Object.defineProperty;var _e=Object.getOwnPropertyDescriptor;var Re=Object.getOwnPropertyNames;var De=Object.prototype.hasOwnProperty;var Oe=(e,s,t)=>s in e?V(e,s,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[s]=t;var Ae=(e,s)=>{for(var t in s)V(e,t,{get:s[t],enumerable:!0})},Ee=(e,s,t,r)=>{if(s&&typeof s=="object"||typeof s=="function")for(let i of Re(s))!De.call(e,i)&&i!==t&&V(e,i,{get:()=>s[i],enumerable:!(r=_e(s,i))||r.enumerable});return e};var Ce=e=>Ee(V({},"__esModule",{value:!0}),e);var R=(e,s,t)=>(Oe(e,typeof s!="symbol"?s+"":s,t),t);var He={};Ae(He,{default:()=>ee});module.exports=Ce(He);var L=class extends Error{constructor(t,r,i){let a=t.status||t.status===0?t.status:"",f=t.statusText||"",n=`${a} ${f}`.trim(),l=n?`status code ${n}`:"an unknown error";super(`Request failed with ${l}: ${r.method} ${r.url}`);R(this,"response");R(this,"request");R(this,"options");this.name="HTTPError",this.response=t,this.request=r,this.options=i}};var q=class extends Error{constructor(t){super(`Request timed out: ${t.method} ${t.url}`);R(this,"request");this.name="TimeoutError",this.request=t}};var G=e=>e!==null&&typeof e=="object";var N=(...e)=>{for(let s of e)if((!G(s)||Array.isArray(s))&&s!==void 0)throw new TypeError("The `options` argument must be an object");return ie({},...e)},se=(e={},s={})=>{let t=new globalThis.Headers(e),r=s instanceof globalThis.Headers,i=new globalThis.Headers(s);for(let[a,f]of i.entries())r&&f==="undefined"||f===void 0?t.delete(a):t.set(a,f);return t};function W(e,s,t){var r,i;return Object.hasOwn(s,t)&&s[t]===void 0?[]:ie((r=e[t])!=null?r:[],(i=s[t])!=null?i:[])}var re=(e={},s={})=>({beforeRequest:W(e,s,"beforeRequest"),beforeRetry:W(e,s,"beforeRetry"),afterResponse:W(e,s,"afterResponse"),beforeError:W(e,s,"beforeError")}),ie=(...e)=>{let s={},t={},r={};for(let i of e)if(Array.isArray(i))Array.isArray(s)||(s=[]),s=[...s,...i];else if(G(i)){for(let[a,f]of Object.entries(i))G(f)&&a in s&&(f=ie(s[a],f)),s={...s,[a]:f};G(i.hooks)&&(r=re(r,i.hooks),s.hooks=r),G(i.headers)&&(t=se(t,i.headers),s.headers=t)}return s};var he=(()=>{let e=!1,s=!1,t=typeof globalThis.ReadableStream=="function",r=typeof globalThis.Request=="function";if(t&&r)try{s=new globalThis.Request("https://empty.invalid",{body:new globalThis.ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type")}catch(i){if(i instanceof Error&&i.message==="unsupported BodyInit type")return!1;throw i}return e&&!s})(),ue=typeof globalThis.AbortController=="function",fe=typeof globalThis.ReadableStream=="function",ge=typeof globalThis.FormData=="function",Q=["get","post","put","patch","head","delete"],Me=()=>{};Me();var me={json:"application/json",text:"text/*",formData:"multipart/form-data",arrayBuffer:"*/*",blob:"*/*"},Y=2147483647,K=Symbol("stop"),ye={json:!0,parseJson:!0,stringifyJson:!0,searchParams:!0,prefixUrl:!0,retry:!0,timeout:!0,hooks:!0,throwHttpErrors:!0,onDownloadProgress:!0,fetch:!0},we={method:!0,headers:!0,body:!0,mode:!0,credentials:!0,cache:!0,redirect:!0,referrer:!0,referrerPolicy:!0,integrity:!0,keepalive:!0,signal:!0,window:!0,dispatcher:!0,duplex:!0,priority:!0};var Te=e=>Q.includes(e)?e.toUpperCase():e,Ie=["get","put","head","delete","options","trace"],je=[408,413,429,500,502,503,504],Be=[413,429,503],be={limit:2,methods:Ie,statusCodes:je,afterStatusCodes:Be,maxRetryAfter:Number.POSITIVE_INFINITY,backoffLimit:Number.POSITIVE_INFINITY,delay:e=>.3*2**(e-1)*1e3},ve=(e={})=>{if(typeof e=="number")return{...be,limit:e};if(e.methods&&!Array.isArray(e.methods))throw new Error("retry.methods must be an array");if(e.statusCodes&&!Array.isArray(e.statusCodes))throw new Error("retry.statusCodes must be an array");return{...be,...e}};async function ne(e,s,t,r){return new Promise((i,a)=>{let f=setTimeout(()=>{t&&t.abort(),a(new q(e))},r.timeout);r.fetch(e,s).then(i).catch(a).then(()=>{clearTimeout(f)})})}async function oe(e,{signal:s}){return new Promise((t,r)=>{s&&(s.throwIfAborted(),s.addEventListener("abort",i,{once:!0}));function i(){clearTimeout(a),r(s.reason)}let a=setTimeout(()=>{s==null||s.removeEventListener("abort",i),t()},e)})}var Fe=(e,s)=>{let t={};for(let r in s)!(r in we)&&!(r in ye)&&!(r in e)&&(t[r]=s[r]);return t};var I=class{constructor(s,t={}){R(this,"request");R(this,"abortController");R(this,"_retryCount",0);R(this,"_input");R(this,"_options");var r,i,a,f,n,l,y,w;if(this._input=s,this._options={...t,headers:se(this._input.headers,t.headers),hooks:re({beforeRequest:[],beforeRetry:[],beforeError:[],afterResponse:[]},t.hooks),method:Te((r=t.method)!=null?r:this._input.method),prefixUrl:String(t.prefixUrl||""),retry:ve(t.retry),throwHttpErrors:t.throwHttpErrors!==!1,timeout:(i=t.timeout)!=null?i:1e4,fetch:(a=t.fetch)!=null?a:globalThis.fetch.bind(globalThis)},typeof this._input!="string"&&!(this._input instanceof URL||this._input instanceof globalThis.Request))throw new TypeError("`input` must be a string, URL, or Request");if(this._options.prefixUrl&&typeof this._input=="string"){if(this._input.startsWith("/"))throw new Error("`input` must not begin with a slash when using `prefixUrl`");this._options.prefixUrl.endsWith("/")||(this._options.prefixUrl+="/"),this._input=this._options.prefixUrl+this._input}if(ue){this.abortController=new globalThis.AbortController;let x=(f=this._options.signal)!=null?f:this._input.signal;x==null||x.addEventListener("abort",()=>{this.abortController.abort(x.reason)}),this._options.signal=this.abortController.signal}if(he&&(this._options.duplex="half"),this._options.json!==void 0&&(this._options.body=(y=(l=(n=this._options).stringifyJson)==null?void 0:l.call(n,this._options.json))!=null?y:JSON.stringify(this._options.json),this._options.headers.set("content-type",(w=this._options.headers.get("content-type"))!=null?w:"application/json")),this.request=new globalThis.Request(this._input,this._options),this._options.searchParams){let _="?"+(typeof this._options.searchParams=="string"?this._options.searchParams.replace(/^\?/,""):new URLSearchParams(this._options.searchParams).toString()),m=this.request.url.replace(/(?:\?.*?)?(?=#|$)/,_);(ge&&this._options.body instanceof globalThis.FormData||this._options.body instanceof URLSearchParams)&&!(this._options.headers&&this._options.headers["content-type"])&&this.request.headers.delete("content-type"),this.request=new globalThis.Request(new globalThis.Request(m,{...this.request}),this._options)}}static create(s,t){let r=new I(s,t),i=async()=>{if(typeof r._options.timeout=="number"&&r._options.timeout>Y)throw new RangeError(`The \`timeout\` option cannot be greater than ${Y}`);await Promise.resolve();let n=await r._fetch();for(let l of r._options.hooks.afterResponse){let y=await l(r.request,r._options,r._decorateResponse(n.clone()));y instanceof globalThis.Response&&(n=y)}if(r._decorateResponse(n),!n.ok&&r._options.throwHttpErrors){let l=new L(n,r.request,r._options);for(let y of r._options.hooks.beforeError)l=await y(l);throw l}if(r._options.onDownloadProgress){if(typeof r._options.onDownloadProgress!="function")throw new TypeError("The `onDownloadProgress` option must be a function");if(!fe)throw new Error("Streams are not supported in your environment. `ReadableStream` is missing.");return r._stream(n.clone(),r._options.onDownloadProgress)}return n},f=r._options.retry.methods.includes(r.request.method.toLowerCase())?r._retry(i):i();for(let[n,l]of Object.entries(me))f[n]=async()=>{r.request.headers.set("accept",r.request.headers.get("accept")||l);let w=(await f).clone();if(n==="json"){if(w.status===204||(await w.clone().arrayBuffer()).byteLength===0)return"";if(t.parseJson)return t.parseJson(await w.text())}return w[n]()};return f}_calculateRetryDelay(s){var r,i,a,f;if(this._retryCount++,this._retryCount>this._options.retry.limit||s instanceof q)throw s;if(s instanceof L){if(!this._options.retry.statusCodes.includes(s.response.status))throw s;let n=(a=(i=(r=s.response.headers.get("Retry-After"))!=null?r:s.response.headers.get("RateLimit-Reset"))!=null?i:s.response.headers.get("X-RateLimit-Reset"))!=null?a:s.response.headers.get("X-Rate-Limit-Reset");if(n&&this._options.retry.afterStatusCodes.includes(s.response.status)){let l=Number(n)*1e3;Number.isNaN(l)?l=Date.parse(n)-Date.now():l>=Date.parse("2024-01-01")&&(l-=Date.now());let y=(f=this._options.retry.maxRetryAfter)!=null?f:l;return l<y?l:y}if(s.response.status===413)throw s}let t=this._options.retry.delay(this._retryCount);return Math.min(this._options.retry.backoffLimit,t)}_decorateResponse(s){return this._options.parseJson&&(s.json=async()=>this._options.parseJson(await s.text())),s}async _retry(s){try{return await s()}catch(t){let r=Math.min(this._calculateRetryDelay(t),Y);if(this._retryCount<1)throw t;await oe(r,{signal:this._options.signal});for(let i of this._options.hooks.beforeRetry)if(await i({request:this.request,options:this._options,error:t,retryCount:this._retryCount})===K)return;return this._retry(s)}}async _fetch(){for(let r of this._options.hooks.beforeRequest){let i=await r(this.request,this._options);if(i instanceof Request){this.request=i;break}if(i instanceof Response)return i}let s=Fe(this.request,this._options),t=this.request;return this.request=t.clone(),this._options.timeout===!1?this._options.fetch(t,s):ne(t,s,this.abortController,this._options)}_stream(s,t){let r=Number(s.headers.get("content-length"))||0,i=0;return s.status===204?(t&&t({percent:1,totalBytes:r,transferredBytes:i},new Uint8Array),new globalThis.Response(null,{status:s.status,statusText:s.statusText,headers:s.headers})):new globalThis.Response(new globalThis.ReadableStream({async start(a){let f=s.body.getReader();t&&t({percent:0,transferredBytes:0,totalBytes:r},new Uint8Array);async function n(){let{done:l,value:y}=await f.read();if(l){a.close();return}if(t){i+=y.byteLength;let w=r===0?0:i/r;t({percent:w,transferredBytes:i,totalBytes:r},y)}a.enqueue(y),await n()}await n()}}),{status:s.status,statusText:s.statusText,headers:s.headers})}};var ae=e=>{let s=(t,r)=>I.create(t,N(e,r));for(let t of Q)s[t]=(r,i)=>I.create(r,N(e,i,{method:t}));return s.create=t=>ae(N(t)),s.extend=t=>(typeof t=="function"&&(t=t(e!=null?e:{})),ae(N(e,t))),s.stop=K,s},qe=ae(),U=qe;var X=require("obsidian");var Ge=e=>({beforeRequest:[async s=>(e.accessToken.token&&(e.accessToken.expiresAt-Date.now()<6e4&&await H(e),s.headers.set("Authorization",`Bearer ${e.accessToken.token}`)),s)],afterResponse:[async(s,t,r)=>r.ok?r:(new X.Notice(`Error: ${await r.text()}`),new Response)]}),Se=e=>U.extend({prefixUrl:"https://www.googleapis.com",hooks:Ge(e),timeout:12e4}),H=async e=>{try{let{expires_in:s,access_token:t}=await U.post("https://ogd.richardxiong.com/api/access",{json:{refresh_token:e.settings.refreshToken}}).json();return e.accessToken={token:t,expiresAt:Date.now()+s*1e3},e.accessToken}catch(s){if(!await $())return new X.Notice("Something is wrong with your internet connection, so we could not fetch a new access token! Once you're back online, please restart Obsidian.",0);e.settings.refreshToken="",e.accessToken={token:"",expiresAt:0},new X.Notice("Something is wrong with your refresh token, please restart Obsidian and then reset it.",0),await e.saveSettings();return}};var z=require("obsidian"),O="application/vnd.google-apps.folder",$e=["graph.json","workspace.json","workspace-mobile.json"],Le=["manifest.json","styles.css","main.js","data.json"],ke=e=>{if(typeof e=="string")return`='${e}'`;if("contains"in e)return` contains '${e.contains}'`;if("not"in e)return`!='${e.not}'`},Ne={name:e=>"name"+ke(e),mimeType:e=>"mimeType"+ke(e),parent:e=>`'${e}' in parents`,starred:e=>`starred=${e}`,query:e=>`fullText contains '${e}'`,properties:e=>Object.entries(e).map(([s,t])=>`properties has { key='${s}' and value='${t}' }`),modifiedTime:e=>{if("eq"in e)return`modifiedTime='${e.eq}'`;if("gt"in e)return`modifiedTime>'${e.gt}'`;if("lt"in e)return`modifiedTime<'${e.lt}'`}};var xe=e=>{let s=Se(e),t=c=>encodeURIComponent(`(${c.map(d=>`(${Object.entries(d).flatMap(([u,b])=>b===void 0?[]:Array.isArray(b)?b.map(T=>[u,T]):[[u,b]]).map(([u,b])=>Ne[u](b)).join(" and ")})`).join(" or ")}) and trashed=false and properties has { key='vault' and value='${e.app.vault.getName()}' }`),r=async({matches:c,pageToken:d,order:h="descending",pageSize:u=30,include:b=["id","name","mimeType","starred","description","properties"]})=>{let T=await s.get(`drive/v3/files?fields=nextPageToken,files(${b.join(",")})&pageSize=${u}&q=${c?t(c):"trashed=false"}${c!=null&&c.find(({query:A})=>A)?"":"&orderBy=name"+(h==="ascending"?"":" desc")}${d?"&pageToken="+d:""}`).json();if(T)return T},i=async(c,d=!1)=>{let h=await r({...c,pageSize:1e3});if(h){for(;h.nextPageToken;){let u=await r({...c,pageToken:h.nextPageToken,pageSize:1e3});if(!u)return;h.files.push(...u.files),h.nextPageToken=u.nextPageToken}return d?h.files:h.files.filter(({properties:u})=>(u==null?void 0:u.obsidian)!=="vault")}},a=async()=>{let c=await i({matches:[{properties:{obsidian:"vault"}}]},!0);if(c){if(c.length)return c[0].id;{let d=await s.post("drive/v3/files",{json:{name:e.app.vault.getName(),mimeType:O,description:"Obsidian Vault: "+e.app.vault.getName(),properties:{obsidian:"vault",vault:e.app.vault.getName()}}}).json();return d?d.id:void 0}}};return{paginateFiles:r,searchFiles:i,getRootFolderId:a,createFolder:async({name:c,parent:d,description:h,properties:u,modifiedTime:b})=>{if(!d&&(d=await a(),!d))return;u||(u={}),u.vault||(u.vault=e.app.vault.getName());let T=await s.post("drive/v3/files",{json:{name:c,mimeType:O,description:h,parents:[d],properties:u,modifiedTime:b}}).json();if(T)return T.id},uploadFile:async(c,d,h,u)=>{if(!h&&(h=await a(),!h))return;u||(u={}),u.properties||(u.properties={}),u.properties.vault||(u.properties.vault=e.app.vault.getName());let b=new FormData;b.append("metadata",new Blob([JSON.stringify({name:d,mimeType:c.type,parents:[h],...u})],{type:"application/json"})),b.append("file",c);let T=await s.post("upload/drive/v3/files?uploadType=multipart&fields=id",{body:b}).json();if(T)return T.id},updateFile:async(c,d,h={})=>{let u=new FormData;u.append("metadata",new Blob([JSON.stringify(h)],{type:"application/json"})),u.append("file",d);let b=await s.patch(`upload/drive/v3/files/${c}?uploadType=multipart&fields=id`,{body:u}).json();if(b)return b.id},updateFileMetadata:async(c,d)=>{let h=await s.patch(`drive/v3/files/${c}`,{json:d}).json();if(h)return h.id},deleteFile:async c=>{if((await s.delete(`drive/v3/files/${c}`)).ok)return!0},getFile:c=>s.get(`drive/v3/files/${c}?alt=media&acknowledgeAbuse=true`),getFileMetadata:c=>s.get(`drive/v3/files/${c}`).json(),idFromPath:async c=>{let d=await i({matches:[{properties:{path:c}}]});if(d!=null&&d.length)return d[0].id},idsFromPaths:async c=>{let d=await i({matches:c.map(h=>({properties:{path:h}}))});if(d)return d.map(h=>({id:h.id,path:h.properties.path}))},getChangesStartToken:async()=>{let c=await s.get("drive/v3/changes/startPageToken").json();if(c)return c.startPageToken},getChanges:async c=>{if(!c)return[];let d=u=>s.get(`drive/v3/changes?${new URLSearchParams({pageToken:u,pageSize:"1000",includeRemoved:"true"}).toString()}`).json(),h=await d(c);if(h){for(;h.nextPageToken;){let u=await d(h.nextPageToken);if(!u)return;h.changes.push(...u.changes),h.newStartPageToken=u.newStartPageToken,h.nextPageToken=u.nextPageToken}return h.changes}},batchDelete:async c=>{let d=new FormData;c.forEach((u,b)=>{let T=["--batch_boundary","Content-Type: application/http","",`DELETE /drive/v3/files/${u} HTTP/1.1`,"",""].join(`\r
`);d.append(`request_${b+1}`,T)}),d.append("","--batch_boundary--");let h=await s.post("batch/drive/v3",{headers:{"Content-Type":"multipart/mixed; boundary=batch_boundary"},body:d}).text();if(h)return h},checkConnection:$,deleteFilesMinimumOperations:async c=>{let d=c.filter(h=>h instanceof z.TFolder);if(d.length){let h=Math.max(...d.map(({path:u})=>u.split("/").length));for(let u=1;u<=h;u++){let b=c.filter(T=>T instanceof z.TFolder&&T.path.split("/").length===u);await Promise.all(b.map(T=>e.deleteFile(T))),b.forEach(T=>c=c.filter(({path:A})=>!A.startsWith(T.path+"/")&&A!==T.path))}}await Promise.all(c.map(h=>e.deleteFile(h)))},getConfigFilesToSync:async()=>{let c=[],{vault:d}=e.app,{adapter:h}=d,[u,b]=await Promise.all([h.list(d.configDir),h.list(d.configDir+"/plugins")]);return await Promise.all(u.files.filter(T=>!$e.includes(Z(T))).map(async T=>{let A=await h.stat(T);((A==null?void 0:A.mtime)||0)>e.settings.lastSyncedAt&&c.push(T)}).concat(b.folders.map(async T=>{let A=await h.list(T);await Promise.all(A.files.filter(J=>Le.includes(Z(J))).map(async J=>{let te=await h.stat(J);((te==null?void 0:te.mtime)||0)>e.settings.lastSyncedAt&&c.push(J)}))}))),c}}},$=async()=>{try{return(await U.get("https://ogd.richardxiong.com/api/ping")).ok}catch(e){return!1}},D=async(e,s=10)=>{let t=[];for(let r=0;r<e.length;r+=s){let i=e.slice(r,r+s);t.push(...await Promise.all(i.map(a=>a())))}return t},C=(e,s,t,r)=>`Syncing (${Math.floor(e+(s-e)*(t/r))}%)`,Z=e=>e.split("/").slice(-1)[0],M=e=>{let s=new Array(Math.max(...e.map(t=>(t instanceof z.TFolder?t.path:t).split("/").length))).fill(0).map(()=>[]);return e.forEach(t=>{s[(t instanceof z.TFolder?t.path:t).split("/").length-1].push(t)}),s};var E=require("obsidian");var j=async(e,s)=>{let t=null;if(!s){if(e.syncing)return;t=await e.startSync()}let{vault:r}=e.app,i=r.adapter;e.accessToken.token||await H(e);let a=await e.drive.searchFiles({include:["id","modifiedTime","properties","mimeType"],matches:[{modifiedTime:{gt:new Date(e.settings.lastSyncedAt).toISOString()}}]});if(!a)return new E.Notice("An error occurred fetching Google Drive files.");let f=await e.drive.getChanges(e.settings.changesToken);if(!f)return new E.Notice("An error occurred fetching Google Drive changes.");let n=f.filter(({removed:m})=>m).map(({fileId:m})=>{let o=e.settings.driveIdToPath[m];if(!o)return;delete e.settings.driveIdToPath[m];let g=r.getAbstractFileByPath(o);if(!g&&e.settings.operations[o]==="delete"){delete e.settings.operations[o];return}return g});if(!a.length&&!n.length)return s?void 0:(e.endSync(t),new E.Notice("You're up to date!"));let l=Object.fromEntries(Object.entries(e.settings.driveIdToPath).map(([m,o])=>[o,m]));a.forEach(({id:m,properties:o})=>{l[o.path]=m}),e.settings.driveIdToPath=Object.fromEntries(Object.entries(l).map(([m,o])=>[o,m])),await(async()=>{let m=n.filter(p=>p instanceof E.TFile).filter(p=>{if(e.settings.operations[p.path]==="modify"){l[p.path]||(e.settings.operations[p.path]="create");return}return!0}),o=n.map(p=>p==null?void 0:p.path),g=n.filter(p=>p instanceof E.TFolder).filter(p=>{if(!l[p.path]){if(p.children.find(({path:F})=>!o.includes(F)))return!0;e.settings.operations[p.path]="create"}});await e.drive.deleteFilesMinimumOperations([...g,...m])})(),t==null||t.setMessage("Syncing (33%)"),await(async()=>{let m=a.filter(({mimeType:p})=>p===O);if(m.length){let p=M(m.map(({properties:F})=>F.path));for(let F of p)await Promise.all(F.map(async v=>{if(delete e.settings.operations[v],!(r.getFolderByPath(v)||await i.exists(v)))return e.createFolder(v)}))}let o=0,g=a.filter(({mimeType:p})=>p!==O);await D(g.map(p=>async()=>{let F=r.getFileByPath(p.properties.path)||await i.exists(p.properties.path),v=e.settings.operations[p.properties.path];if(o++,F&&v==="modify")return;if(F&&v==="create"){e.settings.operations[p.properties.path]="modify";return}let S=await e.drive.getFile(p.id).arrayBuffer();return t==null||t.setMessage(C(33,100,o,g.length)),F instanceof E.TFile?e.modifyFile(F,S,p.modifiedTime):e.upsertFile(p.properties.path,S,p.modifiedTime)}))})(),await(async()=>{let o=(await Promise.all(f.filter(({removed:S})=>S).map(async({fileId:S})=>{let c=e.settings.driveIdToPath[S];if(!c||r.getAbstractFileByPath(c))return;let d=await i.stat(c);if(d)return{path:c,type:d.type}}))).filter(Boolean),g=r.getConfig("trashOption");if(g==="local"||g==="system"){let S=g==="local"?i.trashLocal:i.trashSystem,c=o.filter(d=>d.type==="folder");if(c.length){let d=Math.max(...c.map(({path:h})=>h.split("/").length));for(let h=1;h<=d;h++){let u=o.filter(b=>b.type==="folder"&&b.path.split("/").length===h);await Promise.all(u.map(({path:b})=>S(b))),u.forEach(b=>o=o.filter(({path:T})=>!T.startsWith(b.path+"/")&&T!==b.path))}}return Promise.all(o.map(({path:d})=>S(d)))}let p=o.filter(S=>S.type==="file");await Promise.all(p.map(({path:S})=>i.remove(S)));let F=o.filter(S=>S.type==="folder"),v=M(F.map(({path:S})=>S));v.reverse();for(let S of v)await Promise.all(S.map(async c=>{let d=await i.list(c);d.files.length+d.folders.length||i.rmdir(c,!1)}))})(),!s&&(await e.endSync(t),new E.Notice("Files have been synced from Google Drive!"))};var k=require("obsidian");var ce=class extends k.Modal{constructor(t,r,i){super(t.app);this.proceed=i,this.setTitle("Push confirmation"),this.contentEl.createEl("p").setText("Do you want to push the following changes to Google Drive:");let a=this.contentEl.createEl("div"),f=n=>{a.empty(),n.map(([l,y])=>{let w=a.createDiv();w.addClass("operation-container");let x=w.createEl("p");if(x.createEl("b").setText(`${y[0].toUpperCase()}${y.slice(1)}`),x.createSpan().setText(`: ${l}`),y==="delete"&&n.some(([m])=>l.startsWith(m+"/")))return;let _=w.createDiv().createEl("button");(0,k.setIcon)(_,"trash-2"),_.onclick=async()=>{let m=n.map(([p])=>p).filter(p=>p.startsWith(l+"/")||p===l);if(!await new Promise(p=>{new le(t,y,m,p).open()}))return;m.forEach(p=>delete t.settings.operations[p]);let g=n.filter(([p])=>!m.includes(p));if(!g.length)return this.close();f(g)}})};f(r),new k.Setting(this.contentEl).addButton(n=>n.setButtonText("Cancel").onClick(()=>this.close())).addButton(n=>n.setButtonText("Confirm").setCta().onClick(()=>{i(!0),this.close()}))}onClose(){this.proceed(!1)}},le=class extends k.Modal{constructor(t,r,i,a){super(t.app);this.t=t,this.filePathToId=Object.fromEntries(Object.entries(this.t.settings.driveIdToPath).map(([n,l])=>[l,n]));let f={create:"creating",delete:"deleting",modify:"modifying"};this.setTitle("Undo confirmation"),this.contentEl.createEl("p").setText(`Are you sure you want to undo ${f[r]} the following file(s):`),this.contentEl.createEl("ul").append(...i.map(n=>{let l=this.contentEl.createEl("li");return l.addClass("operation-file"),l.setText(n),l})),this.proceed=a,new k.Setting(this.contentEl).addButton(n=>n.setButtonText("Cancel").onClick(()=>this.close())).addButton(n=>n.setButtonText("Confirm").setCta().onClick(async()=>{n.setDisabled(!0),r==="delete"&&await this.handleDelete(i),r==="create"&&await this.handleCreate(i[0]),r==="modify"&&await this.handleModify(i[0]),a(!0),this.close()}))}onClose(){this.proceed(!1)}async handleDelete(t){let r=await this.t.drive.searchFiles({include:["id","mimeType","properties","modifiedTime"],matches:t.map(n=>({properties:{path:n}}))});if(!r)return new k.Notice("An error occurred fetching Google Drive files.");let i=Object.fromEntries(r.map(n=>[n.properties.path,n])),a=t.filter(n=>i[n].properties.path===O);if(a.length){let n=M(a);for(let l of n)await Promise.all(l.map(y=>this.t.createFolder(y)))}let f=t.filter(n=>i[n].properties.path!==O);await D(f.map(n=>async()=>{let l=await this.t.drive.getFile(this.filePathToId[n]).arrayBuffer();return l?this.t.createFile(n,l,i[n].modifiedTime):new k.Notice("An error occurred fetching Google Drive files.")}))}async handleCreate(t){let r=this.app.vault.getAbstractFileByPath(t);if(r)return this.t.deleteFile(r)}async handleModify(t){let r=this.app.vault.getFileByPath(t);if(!r)return;let[i,a]=await Promise.all([this.t.drive.getFile(this.filePathToId[t]).arrayBuffer(),this.t.drive.getFileMetadata(this.filePathToId[t])]);return!i||!a?new k.Notice("An error occurred fetching Google Drive files."):this.t.modifyFile(r,i,a.modifiedTime)}},pe=async e=>{if(e.syncing)return;let s=Object.entries(e.settings.operations).sort(([o],[g])=>o<g?-1:o>g?1:0),{vault:t}=e.app,r=t.adapter;if(!await new Promise(o=>{new ce(e,s,o).open()}))return;let a=await e.startSync();await j(e,!0);let f=Object.entries(e.settings.operations),n=f.filter(([o,g])=>g==="delete"),l=f.filter(([o,g])=>g==="create"),y=f.filter(([o,g])=>g==="modify"),w=Object.fromEntries(Object.entries(e.settings.driveIdToPath).map(([o,g])=>[g,o])),x=await e.drive.searchFiles({include:["properties"],matches:[{properties:{config:"true"}}]});if(!x)return new k.Notice("An error occurred fetching Google Drive files.");if(await Promise.all(x.map(async({properties:o})=>{await r.exists(o.path)||n.push([o.path,"delete"])})),n.length){if(!await e.drive.batchDelete(n.map(([g])=>w[g])))return new k.Notice("An error occurred deleting Google Drive files.");n.forEach(([g])=>delete e.settings.driveIdToPath[g])}if(a.setMessage("Syncing (33%)"),l.length){let o=0,g=l.map(([v])=>t.getAbstractFileByPath(v)),p=g.filter(v=>v instanceof k.TFolder);if(p.length){let v=M(p);for(let S of v)await D(S.map(c=>async()=>{let d=await e.drive.createFolder({name:c.name,parent:c.parent?w[c.parent.path]:void 0,properties:{path:c.path},modifiedTime:new Date().toISOString()});if(!d)return new k.Notice("An error occurred creating Google Drive folders.");o++,a.setMessage(C(33,66,o,g.length)),e.settings.driveIdToPath[d]=c.path,w[c.path]=d}))}let F=g.filter(v=>v instanceof k.TFile);await D(F.map(v=>async()=>{let S=await e.drive.uploadFile(new Blob([await t.readBinary(v)]),v.name,v.parent?w[v.parent.path]:void 0,{properties:{path:v.path},modifiedTime:new Date().toISOString()});if(!S)return new k.Notice("An error occurred creating Google Drive files.");o++,a.setMessage(C(33,66,o,g.length)),e.settings.driveIdToPath[S]=v.path}))}if(y.length){let o=0,g=y.map(([F])=>t.getFileByPath(F)).filter(F=>F instanceof k.TFile),p=Object.fromEntries(Object.entries(e.settings.driveIdToPath).map(([F,v])=>[v,F]));await D(g.map(F=>async()=>{if(!await e.drive.updateFile(p[F.path],new Blob([await t.readBinary(F)]),{modifiedTime:new Date().toISOString()}))return new k.Notice("An error occurred modifying Google Drive files.");o++,a.setMessage(C(66,99,o,g.length))}))}let _=await e.drive.getConfigFilesToSync(),m=new Set;if(_.forEach(o=>{let g=o.split("/");for(let p=1;p<g.length;p++)m.add(g.slice(0,p).join("/"))}),m.forEach(o=>{w[o]&&m.delete(o)}),m.size){let o=M(Array.from(m));for(let g of o)await D(g.map(p=>async()=>{let F=await e.drive.createFolder({name:p.split("/").pop()||"",parent:w[p.split("/").slice(0,-1).join("/")],properties:{path:p,config:"true"},modifiedTime:new Date().toISOString()});if(!F)return new k.Notice("An error occurred creating Google Drive folders.");e.settings.driveIdToPath[F]=p,w[p]=F}))}await D(_.map(o=>async()=>{if(w[o]){await e.drive.updateFile(w[o],new Blob([await r.readBinary(o)]),{modifiedTime:new Date().toISOString()});return}let g=await e.drive.uploadFile(new Blob([await r.readBinary(o)]),Z(o),w[o.split("/").slice(0,-1).join("/")],{properties:{path:o,config:"true"},modifiedTime:new Date().toISOString()});if(!g)return new k.Notice("An error occurred creating Google Drive config files.");e.settings.driveIdToPath[g]=o,w[o]=g})),await e.drive.updateFile(w[t.configDir+"/plugins/google-drive-sync/data.json"],new Blob([JSON.stringify(e.settings,null,2)]),{modifiedTime:new Date().toISOString()}),e.settings.operations={},await e.endSync(a,!1),new k.Notice("Sync complete!")};var B=require("obsidian");var Pe=async e=>{if(e.syncing)return;let s=await e.startSync();await j(e,!0);let{vault:t}=e.app,r=Object.entries(e.settings.operations),i=r.filter(([l,y])=>y==="delete"),a=r.filter(([l,y])=>y==="create"),f=r.filter(([l,y])=>y==="modify"),n=Object.fromEntries(Object.entries(e.settings.driveIdToPath).map(([l,y])=>[y,l]));if(a.length&&await e.drive.deleteFilesMinimumOperations(a.map(([l])=>t.getAbstractFileByPath(l)).filter(l=>l instanceof B.TAbstractFile)),s.setMessage("Syncing (33%)"),f.length){let l=0,y=f.map(([w])=>t.getFileByPath(w));await D(y.map(w=>async()=>{let[x,_]=await Promise.all([e.drive.getFile(n[w.path]).arrayBuffer(),e.drive.getFileMetadata(n[w.path])]);return!x||!_?new B.Notice("An error occurred fetching Google Drive files."):(l++,s.setMessage(C(33,66,l,y.length)),e.modifyFile(w,x,_.modifiedTime))}))}if(i.length){let l=await e.drive.searchFiles({include:["id","mimeType","properties","modifiedTime"],matches:i.map(([m])=>({properties:{path:m}}))});if(!l)return new B.Notice("An error occurred fetching Google Drive files.");let y=Object.fromEntries(l.map(m=>[m.properties.path,m])),w=i.filter(([m])=>y[m].mimeType===O);if(w.length){let m=M(w.map(([o])=>o));for(let o of m)await Promise.all(o.map(g=>e.createFolder(g)))}let x=0,_=i.filter(([m])=>y[m].mimeType!==O);await D(_.map(([m])=>async()=>{let o=await e.drive.getFile(n[m]).arrayBuffer();return o?(x++,s.setMessage(C(66,99,x,_.length)),e.createFile(m,o,y[m].modifiedTime)):new B.Notice("An error occurred fetching Google Drive files.")}))}e.settings.operations={},await e.endSync(s),new B.Notice("Reset complete.")};var P=require("obsidian"),Ue={refreshToken:"",operations:{},driveIdToPath:{},lastSyncedAt:0,changesToken:""},ee=class extends P.Plugin{constructor(){super(...arguments);this.accessToken={token:"",expiresAt:0};this.drive=xe(this);this.debouncedSaveSettings=(0,P.debounce)(this.saveSettings.bind(this),500,!0)}async onload(){let{vault:t}=this.app;if(await this.loadSettings(),this.addSettingTab(new de(this.app,this)),!this.settings.refreshToken){new P.Notice("Please add your refresh token to Google Drive Sync through our website or our readme/this plugin's settings. If you haven't already, PLEASE read through this plugin's readme or website CAREFULLY for instructions on how to use this plugin. If you don't know what you're doing, your data could get DELETED.",0);return}this.ribbonIcon=this.addRibbonIcon("refresh-cw","Push to Google Drive",()=>pe(this)),this.addCommand({id:"push",name:"Push to Google Drive",callback:()=>pe(this)}),this.addCommand({id:"pull",name:"Pull from Google Drive",callback:()=>j(this)}),this.addCommand({id:"reset",name:"Reset local vault to Google Drive",callback:()=>Pe(this)}),this.registerEvent(this.app.workspace.on("quit",()=>this.saveSettings())),this.app.workspace.onLayoutReady(()=>this.registerEvent(t.on("create",this.handleCreate.bind(this)))),this.registerEvent(t.on("delete",this.handleDelete.bind(this))),this.registerEvent(t.on("modify",this.handleModify.bind(this))),this.registerEvent(t.on("rename",this.handleRename.bind(this))),$().then(async r=>{r&&(this.syncing=!0,this.ribbonIcon.addClass("spin"),await j(this,!0),await this.endSync())})}onunload(){return this.saveSettings()}async loadSettings(){this.settings=Object.assign({},Ue,await this.loadData())}saveSettings(){return this.saveData(this.settings)}handleCreate(t){this.settings.operations[t.path]==="delete"?t instanceof P.TFile?this.settings.operations[t.path]="modify":delete this.settings.operations[t.path]:this.settings.operations[t.path]="create",this.debouncedSaveSettings()}handleDelete(t){this.settings.operations[t.path]==="create"?delete this.settings.operations[t.path]:this.settings.operations[t.path]="delete",this.debouncedSaveSettings()}handleModify(t){let r=this.settings.operations[t.path];r==="create"||r==="modify"||(this.settings.operations[t.path]="modify",this.debouncedSaveSettings())}handleRename(t,r){this.handleDelete({...t,path:r}),this.handleCreate(t),this.debouncedSaveSettings()}async createFolder(t){let r=this.settings.operations[t];await this.app.vault.createFolder(t),this.settings.operations[t]=r,r||delete this.settings.operations[t]}async createFile(t,r,i){let a=this.settings.operations[t];typeof i=="string"&&(i=new Date(i)),i instanceof Date&&(i=i.getTime()),await this.app.vault.createBinary(t,r,{mtime:i}),this.settings.operations[t]=a,a||delete this.settings.operations[t]}async modifyFile(t,r,i){let a=this.settings.operations[t.path];typeof i=="string"&&(i=new Date(i)),i instanceof Date&&(i=i.getTime()),await this.app.vault.modifyBinary(t,r,{mtime:i}),this.settings.operations[t.path]=a,a||delete this.settings.operations[t.path]}async upsertFile(t,r,i){let a=this.settings.operations[t];typeof i=="string"&&(i=new Date(i)),i instanceof Date&&(i=i.getTime()),await this.app.vault.adapter.writeBinary(t,r,{mtime:i}),this.settings.operations[t]=a,a||delete this.settings.operations[t]}async deleteFile(t){let r=this.settings.operations[t.path];await this.app.fileManager.trashFile(t),delete this.settings.operations[t.path],r||delete this.settings.operations[t.path]}async startSync(){if(!await $())throw new P.Notice("You are not connected to the internet, so you cannot sync right now. Please try syncing once you have connection again.");return this.ribbonIcon.addClass("spin"),this.syncing=!0,new P.Notice("Syncing (0%)",0)}async endSync(t,r=!0){if(r){let a=await this.drive.getConfigFilesToSync();this.settings.lastSyncedAt=Date.now(),await Promise.all(a.map(async f=>this.app.vault.adapter.writeBinary(f,await this.app.vault.adapter.readBinary(f),{mtime:Date.now()})))}else this.settings.lastSyncedAt=Date.now();let i=await this.drive.getChangesStartToken();if(!i)return new P.Notice("An error occurred fetching Google Drive changes token.");this.settings.changesToken=i,await this.saveSettings(),this.ribbonIcon.removeClass("spin"),this.syncing=!1,t==null||t.hide()}},de=class extends P.PluginSettingTab{constructor(t,r){super(t,r);this.plugin=r}display(){let{containerEl:t}=this,{vault:r}=this.app;t.empty(),t.createEl("a",{href:"https://ogd.richardxiong.com",text:"Get refresh token"}),new P.Setting(t).setName("Refresh token").setDesc("A refresh token is required to access your Google Drive for syncing. We suggest cloning your Google Drive vault to the current vault BEFORE syncing.").addText(i=>{let a=()=>(this.plugin.settings.refreshToken="",i.setValue(""),this.plugin.saveSettings());i.setPlaceholder("Enter your refresh token").setValue(this.plugin.settings.refreshToken).onChange(async f=>{if(this.plugin.settings.refreshToken=f,!f)return this.plugin.debouncedSaveSettings();if(!await H(this.plugin)){i.setValue("");return}if(r.getAllLoadedFiles().filter(({path:l})=>l!=="/").length>0)return new P.Notice("Your current vault is not empty! If you want our plugin to handle the initial sync, you have to clear out the current vault. Check the readme or website for more details.",0),a();let n=await this.plugin.drive.getChangesStartToken();if(!n)return new P.Notice("An error occurred fetching Google Drive changes token.");this.plugin.settings.changesToken=n,await this.plugin.saveSettings(),new P.Notice("Refresh token saved! Reload Obsidian to activate sync.",0)})})}};
/*! Bundled license information:

ky/distribution/index.js:
  (*! MIT License © Sindre Sorhus *)
*/

/* nosourcemap */