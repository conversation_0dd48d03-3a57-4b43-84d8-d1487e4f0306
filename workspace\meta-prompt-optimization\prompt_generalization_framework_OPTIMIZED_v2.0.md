# prompt_generalization_framework_OPTIMIZED_v2.0

**Version Control:**
- Version: 2.0 (基於V1+優化版)
- Created: 2025-07-21
- Purpose: 基於V1+整合後續創新的高效能meta-prompt，濃縮設計控制在1800 tokens內
- Base: V1+ + V3.1三層架構 + Token壓縮技術

## PURPOSE
本prompt是供AI參考的指示模板，用於標準化其他prompt設計。基於V1+完整指導體系，整合三層分離架構與自適應機制，確保結構清晰、命名一致且內容規範化。適用於創建高效能、可重複使用的提示詞。

## CORE_FRAMEWORK

### 模組分類標準
**核心模組** (必要組件):
- `PURPOSE` - 提示詞目標與適用場景，明文說明"本prompt是供AI參考的指示模板"
- **架構核心** - 複雜任務用三層分離架構，簡單任務用基本CORE_FRAMEWORK
- `ADAPTIVE_PROTOCOLS` - 自適應處理機制與具體指導
- `OUTPUT_SPECIFICATIONS` - 自然語言執行指令
- `USER_SUPPLEMENTARY` - 使用者增補區域(留白)

**選用模組** (按需添加):
- `QUALITY_ASSURANCE_PROTOCOLS` - 品質保證機制
- `EXAMPLES` - 應用範例展示
- `VERSION_CONTROL` - 版本資訊記錄

### 三層分離架構 (複雜任務適用)
**🔧 META_EXECUTION_LAYER**
- MODE_SELECTION: 自動選擇架構模式(三層分離 vs 基本架構)
- TASK_RECOGNITION: 識別prompt類型與複雜度
- ROLE_DEFINITION: 明確AI執行角色，避免命名域混淆

**📝 SUB_GENERATION_LAYER**  
- TEMPLATE_STRUCTURE: 標準模組命名與組織
- DOMAIN_ADAPTATION: 根據類型調整架構複雜度

**🎯 OUTPUT_SPECIFICATION_LAYER**
- QUALITY_REQUIREMENTS: 符合標準的品質要求
- FORMAT_SPECIFICATIONS: 模組結構與檢查清單

### 內容組織標準
**模組順序**: PURPOSE → 架構核心 → ADAPTIVE_PROTOCOLS → 選用模組 → OUTPUT_SPECIFICATIONS → USER_SUPPLEMENTARY

**結構層級**: 模組(##) → 主分類(###) → 次分類(####)，避免過度細分

**呈現規範**: 概念解釋用段落，步驟指引用條列，比較分析用表格

### 語言格式標準
- 模組名稱: 英文大寫+下劃線 (CORE_FRAMEWORK)
- 中英混用: 中英間空格，術語中英對照
- 重要概念: **粗體**標示

## ADAPTIVE_PROTOCOLS

### 任務複雜度自適應
**簡單任務**: 單一目標+明確需求 → 基礎結構+核心功能
**中等任務**: 多重目標+部分模糊 → 標準結構+擴展功能  
**複雜任務**: 多層目標+高度模糊 → 完整結構+三層架構

### 提示詞類型調整
**分析型**: 強化分析方法論 & 客觀性要求 & 結構化輸出
**創作型**: 明確風格受眾 & 創意發想框架 & 多樣化範例
**技術型**: 技術規範限制 & 錯誤處理機制 & 技術文檔格式

### 評估優化指南
**有效性評估**: 
- 零脈絡測試必須通過
- 檢視回應符合預期目標
- 評估完整性+準確性+相關性

**優化方向**:
- 針對模糊區域增加具體指導
- 🚫立竿見影效果保護: 實用功能不得僅為精簡而移除
- 🔧主動優化責任: AI應主動提供觀察到的優化建議

### 跨提示詞一致性
**術語統一**: 相關prompt集合使用一致專業術語+建立術語表
**結構對齊**: 相似功能保持相似模組結構+共通概念同層級
**風格協調**: 語氣詞彙一致+指令表達統一+專業性水平一致

## QUALITY_ASSURANCE_PROTOCOLS

### 檢查清單
**基礎檢查**:
□ PURPOSE明確說明"本prompt是供AI參考的指示模板"
□ 核心模組完整(PURPOSE+架構核心+ADAPTIVE_PROTOCOLS+OUTPUT_SPECIFICATIONS+USER_SUPPLEMENTARY)
□ 模組命名使用UPPERCASE_WITH_UNDERSCORES格式

**架構檢查**:
□ 正確選擇三層分離架構或基本架構
□ 自適應機制具備判斷能力
□ 跨提示詞一致性保持

**品質檢查**:
□ 零脈絡測試通過
□ 立竿見影效果保護
□ Token效率平衡功能完整性與簡潔性

### 錯誤預防
- 避免已捨棄架構模式和混淆命名
- 確保與當前標準一致
- 監控token使用效率

## EXAMPLES

### 基本應用
```
輸入: 創建社交媒體內容生成prompt
輸出: 基於框架標準化的完整prompt，包含角色定位+流程描述+自然語言指令
```

### 複雜應用  
```
輸入: 多領域知識整合分析型prompt
輸出: 採用三層分離架構的結構化分析框架，包含多層次分析維度+跨領域整合指導
```

## USER_SUPPLEMENTARY
[使用者可在此快速增加特殊條件或需求]

## OUTPUT_SPECIFICATIONS
應用提示詞標準化框架對以下內容進行標準化。根據任務複雜度自動選擇適當架構(三層分離或基本架構)，確保結構清晰且功能完整，遵循所有品質保證標準。
