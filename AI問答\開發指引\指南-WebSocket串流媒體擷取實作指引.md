---
title: 指南-WebSocket串流媒體擷取實作指引
created: 2025-06-25
modified: 2025-06-25
version: "1.0"
tags: [主題/技術/網路程式設計, 主題/技術/串流媒體, 類型/指南, 狀態/已完成, 備註/AI生成]
aliases: [WebSocket串流, 媒體擷取, 串流技術]
---

# 指南-WebSocket串流媒體擷取實作指引

> [!note] 彙整者備註:
>

> [!note] 筆記目的
> 本筆記提供擷取基於WebSocket的串流媒體實作方法，適用於找不到傳統m3u8但發現網站使用WebSocket傳輸的情況。以實務需求為導向，提供完整工作流程。

## 🔍 WebSocket 技術基礎

WebSocket 是一種在單一TCP連線上進行全雙工通訊的協定，讓伺服器能主動向客戶端傳送資訊。

> [!tip] 關鍵特點
> - **持久連線**：建立後保持開啟，不需反覆請求
> - **雙向通訊**：客戶端與伺服器可同時傳送資料
> - **低延遲**：減少HTTP請求負擔，更適合即時應用

---

## 🛠️ 實作前準備

### 需要的工具

| 工具類型 | 推薦選項 | 用途 |
|---------|---------|------|
| 網路分析 | Chrome/Firefox 開發者工具 | 尋找WebSocket連線 |
| 代理工具 | mitmproxy, Fiddler, Charles | 攔截並分析WS通訊 |
| 程式語言 | Python, Node.js, Go | 建立WebSocket客戶端 |
| 媒體處理 | FFmpeg | 處理擷取的媒體資料 |

### 必要知識點

- HTTP與WebSocket協定基礎
- 基本程式開發能力
- FFmpeg使用經驗
- 網路封包分析基礎

---

## 📝 實作流程

### 1. 探索與分析階段

> [!important]
> 找出並理解WebSocket通訊是成功擷取的關鍵第一步！

<details>
<summary>詳細步驟</summary>

1. **開啟開發者工具**：
   - Chrome/Firefox瀏覽器按F12
   - 切換至「Network」分頁
   - 在篩選器中選擇「WS」或輸入「websocket」

2. **尋找WebSocket連線**：
   - 播放目標媒體，觀察新建立的連線
   - 尋找類似「wss://」開頭的連線

3. **分析連線特性**：
   - 檢查連線的請求標頭，尤其是授權相關資訊
   - 觀察訊息格式（二進制/文字）
   - 辨識媒體資料封包的特徵

4. **記錄關鍵資訊**：
   - WebSocket伺服器端點URL
   - 認證令牌或Cookie（如有需要）
   - 訊息格式與協定細節
</details>

### 2. 攔截與解析

使用代理工具深入分析WebSocket通訊：

```bash
# 啟動mitmproxy
mitmproxy -p 8080 --set stream_websockets=true
```

#### 使用Python擷取WebSocket資料

```python
import asyncio
import websockets
import json
import logging

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def capture_websocket_stream(uri, output_file, auth_token=None):
    """連接到WebSocket並擷取媒體串流資料"""
    logger.info(f"嘗試連接到: {uri}")

    # 準備headers (如需身份驗證)
    headers = {}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"

    try:
        async with websockets.connect(uri, extra_headers=headers) as ws:
            logger.info("WebSocket連線已建立")

            # 可能需要發送初始訊息或訂閱特定頻道
            await ws.send(json.dumps({
                "action": "subscribe",
                "channel": "media_stream"
            }))

            with open(output_file, 'wb') as f:
                while True:
                    try:
                        message = await ws.recv()
                        # 解析訊息並提取媒體資料
                        media_data = extract_media_data(message)
                        if media_data:
                            f.write(media_data)
                            logger.debug(f"寫入 {len(media_data)} 位元組資料")
                    except Exception as e:
                        logger.error(f"接收訊息時發生錯誤: {e}")
                        break
    except Exception as e:
        logger.error(f"連線失敗: {e}")

def extract_media_data(message):
    """
    從WebSocket訊息中提取媒體資料
    這需要根據特定網站的協定進行客製化
    """
    # 判斷訊息類型 (二進制或文字)
    if isinstance(message, bytes):
        # 直接二進制資料範例
        return message
    else:
        # 文字訊息 (可能是JSON)
        try:
            data = json.loads(message)
            # 示例：從JSON結構中提取媒體資料位元組
            if "media" in data and "content" in data["media"]:
                import base64
                return base64.b64decode(data["media"]["content"])
        except:
            pass
    return None

# 執行範例
if __name__ == "__main__":
    asyncio.run(capture_websocket_stream(
        "wss://example.com/stream",
        "output_stream.bin",
        auth_token="your_token_here"
    ))
```

### 3. 媒體處理與儲存

> [!warning] 即時處理
> 對於長時間的直播，建議採用即時處理方式，避免中間檔案過大！

<details>
<summary>即時處理方法</summary>

```python
import asyncio
import websockets
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def stream_to_ffmpeg(uri, output_file, auth_token=None):
    """直接串流WebSocket資料到FFmpeg進行處理"""

    # 啟動FFmpeg子程序
    ffmpeg_cmd = [
        'ffmpeg',
        '-i', 'pipe:0',      # 從標準輸入讀取
        '-c:v', 'copy',      # 視訊直接複製，不重新編碼
        '-c:a', 'copy',      # 音訊直接複製，不重新編碼
        '-f', 'mp4',         # 輸出格式
        output_file          # 輸出檔案
    ]

    ffmpeg_process = subprocess.Popen(
        ffmpeg_cmd,
        stdin=subprocess.PIPE,
        stderr=subprocess.PIPE
    )

    logger.info(f"FFmpeg程序已啟動，輸出到: {output_file}")

    # 連接WebSocket
    headers = {}
    if auth_token:
        headers["Authorization"] = f"Bearer {auth_token}"

    try:
        async with websockets.connect(uri, extra_headers=headers) as ws:
            logger.info("WebSocket連線已建立")

            # 可能需要的初始化訊息
            await ws.send(json.dumps({"action": "subscribe"}))

            while True:
                try:
                    message = await ws.recv()
                    media_data = extract_media_data(message)
                    if media_data:
                        ffmpeg_process.stdin.write(media_data)
                        # 定期刷新以確保資料寫入
                        ffmpeg_process.stdin.flush()
                except Exception as e:
                    logger.error(f"接收或處理訊息時發生錯誤: {e}")
                    break
    except Exception as e:
        logger.error(f"連線失敗: {e}")
    finally:
        # 關閉FFmpeg輸入流，結束處理
        if ffmpeg_process.stdin:
            ffmpeg_process.stdin.close()
        # 等待FFmpeg結束
        ffmpeg_process.wait()
        logger.info("FFmpeg處理完成")
```
</details>

---

## 💡 常見問題與解決方案

### 問題類型對照表

| 問題 | 可能原因 | 解決方案 |
|------|---------|---------|
| 連線被拒絕 | 缺少認證、IP封鎖 | 檢查headers、使用代理 |
| 資料格式不明 | 特殊編碼、加密 | 分析JavaScript源碼、嘗試不同解析方法 |
| 連線中斷 | 心跳包缺失、伺服器限制 | 實現心跳機制、處理重連邏輯 |
| FFmpeg無法處理 | 格式不兼容、資料不完整 | 先儲存原始資料、嘗試不同FFmpeg參數 |

> [!tip] 故障排除方法
> - 使用`console.log`或Python的`logging`模組詳細記錄每一步
> - 將原始WebSocket訊息儲存下來供後續分析
> - 比對正常瀏覽器行為與你的程式行為差異

---

## 🔄 進階技巧

### 1. 實現自動重連機制

```python
async def websocket_with_reconnect(uri, handler, max_retries=5):
    retries = 0
    while retries < max_retries:
        try:
            async with websockets.connect(uri) as ws:
                # 重設重試計數
                retries = 0
                await handler(ws)
        except websockets.exceptions.ConnectionClosed:
            logger.warning("連線已關閉，嘗試重新連接...")
            retries += 1
            # 等待時間隨重試次數增加
            await asyncio.sleep(min(2 ** retries, 60))
        except Exception as e:
            logger.error(f"發生錯誤: {e}")
            retries += 1
            await asyncio.sleep(min(2 ** retries, 60))
```

### 2. 處理特殊格式

<details>
<summary>常見特殊格式處理方法</summary>

#### 分段式二進制資料

有些WebSocket串流會將媒體資料分成多個片段傳送，需要重組：

```python
buffer = bytearray()

def process_fragmented_data(message, is_final_fragment):
    global buffer

    # 添加到緩衝區
    buffer.extend(message)

    # 如果是最後一個片段，處理完整資料
    if is_final_fragment:
        complete_data = bytes(buffer)
        buffer = bytearray()  # 重設緩衝區
        return complete_data

    return None
```

#### MPEG-TS 封包處理

```python
def extract_ts_packets(data):
    """從資料流提取標準MPEG-TS封包（每個封包188位元組）"""
    packets = []

    # MPEG-TS封包固定為188位元組，起始位元組為0x47 (71)
    i = 0
    while i <= len(data) - 188:
        if data[i] == 0x47:  # 找到同步位元組
            packets.append(data[i:i+188])
            i += 188
        else:
            # 尋找下一個同步位元組
            sync_pos = data.find(b'\x47', i+1)
            if sync_pos == -1:
                break
            i = sync_pos

    return packets
```
</details>

---

## 📚 相關資源

> [!note] 工具參考
> - **mitmproxy**: [https://mitmproxy.org/](https://mitmproxy.org/) - 強大的HTTP/HTTPS分析工具
> - **websockets (Python庫)**: [https://websockets.readthedocs.io/](https://websockets.readthedocs.io/)
> - **FFmpeg**: [https://ffmpeg.org/](https://ffmpeg.org/) - 視訊處理瑞士刀
> - **Video HTML Capture**: [https://gist.github.com/jim60105/0b3cbc1ab1fbd3e257fd1d5a2b9a03c6](https://gist.github.com/jim60105/0b3cbc1ab1fbd3e257fd1d5a2b9a03c6)

### 學習資源

- WebSocket協定: [RFC 6455](https://tools.ietf.org/html/rfc6455)
- FFmpeg文件: [FFmpeg Documentation](https://ffmpeg.org/documentation.html)
- Python非同步程式設計: [asyncio文件](https://docs.python.org/zh-tw/3/library/asyncio.html)

---

## 🧩 範例專案結構

<details>
<summary>專案檔案結構</summary>

```
websocket_media_capture/
├── README.md                     # 專案說明
├── requirements.txt              # 相依套件
├── config.yaml                   # 配置檔案
├── websocket_capture.py          # 主要實作
├── utils/
│   ├── __init__.py
│   ├── media_processor.py        # 媒體處理工具
│   ├── websocket_client.py       # WebSocket客戶端
│   └── logger.py                 # 日誌工具
└── examples/
    ├── simple_capture.py         # 簡單擷取範例
    └── live_streaming.py         # 直播處理範例
```
</details>

---

## 📌 法律與道德考量

> [!warning] 重要提醒
> 擷取和下載串流媒體可能涉及版權問題。請確保你的行為符合：
> - 著作權法律規範
> - 網站服務條款
> - 個人使用而非商業用途
> - 不用於未經授權的內容分發

---

#網路技術 #串流媒體 #WebSocket #Python #FFmpeg #直播
